using UnityEngine;
using System.Collections.Generic;
using System;
using FMODUnity;
using Random = UnityEngine.Random;

public interface ISoulCreatureBoostProvider
{
    float GetFlightBoostValue();
}

public class SoulCreatureLogic : MonoBehaviour, ISoulCreatureBoostProvider
{
    // Static registry of all active SoulCreatureLogic instances
    private static readonly List<SoulCreatureLogic> ActiveInstances = new List<SoulCreatureLogic>();

    // Static method to get all active instances
    public static List<SoulCreatureLogic> GetAllActiveInstances()
    {
        return ActiveInstances;
    }

    #region Core Components and References

    // Core references
    private Transform playerTransform;
    private PlayerController playerController;
    private ParticleSystem ps;
    private Camera mainCamera;

    // Boundary values for movement constraints
    [SerializeField] public float minY = 20;
    [SerializeField] public float maxY = 50;

    // Public access to average particle position
    [HideInInspector] public Vector3 averageParticlePosition;

    [Header("Particle System Settings")]
    [Tooltip("Minimum number of max particles")]
    [SerializeField] private int minMaxParticles = 200;

    [Tooltip("Maximum number of max particles")]
    [SerializeField] private int maxMaxParticles = 1000;

    // Color settings for ocean creatures
    [Header("Ocean Creature Color Settings")]
    [Tooltip("Whether this is an ocean-type creature that should have random colors")]
    public bool isOceanCreature = false;

    [Tooltip("Minimum color hue (0-1)")]
    [Range(0f, 1f)] public float minHue = 0.5f;

    [Tooltip("Maximum color hue (0-1)")]
    [Range(0f, 1f)] public float maxHue = 0.7f;

    [Tooltip("Saturation for the color (0-1)")]
    [Range(0f, 1f)] public float colorSaturation = 0.8f;

    [Tooltip("Brightness for the color (0-1)")]
    [Range(0f, 1f)] public float colorBrightness = 0.8f;

    // Render queue settings for ocean creatures
    [Header("Render Queue Settings")]
    [Tooltip("Whether to use the centralized render queue system in GameManager")]
    public bool useCentralizedRenderQueue = true;

    #endregion

    #region Module Toggles

    [Header("Module Toggles")]
    [Tooltip("Enable/disable particle following behavior")]
    public bool enableParticleFollowing = true;

    [Tooltip("Enable/disable new weighted movement system")]
    public bool enableWeightedMovement = true;

    [Tooltip("Enable/disable legacy wandering behavior (only used when weighted movement is disabled)")]
    public bool enableWandering = false;

    [Tooltip("Enable/disable legacy player movement effects (only used when weighted movement is disabled)")]
    public bool enablePlayerMovementEffects = false;

    #endregion

    #region Weighted Movement System

    [Header("Weighted Movement Behaviors (0-1 range)")]
    [Tooltip("Weight for waypoint-based wandering behavior")]
    [Range(0f, 1f)] public float waypointWeight = 0.3f;

    [Tooltip("Weight for player movement mirroring behavior")]
    [Range(0f, 1f)] public float playerMirroringWeight = 0.2f;

    [Tooltip("Weight for direct attraction to player position")]
    [Range(0f, 1f)] public float playerAttractionWeight = 0.1f;

    [Tooltip("Weight for playful dolphin lunge behavior")]
    [Range(0f, 1f)] public float dolphinLungeWeight = 0.1f;

    [Tooltip("Weight for inter-creature flocking behavior")]
    [Range(0f, 1f)] public float flockingWeight = 0.2f;

    [Tooltip("Weight for player orbiting behavior")]
    [Range(0f, 1f)] public float orbitingWeight = 0.1f;

    [Header("Dolphin Lunge Settings")]
    [Tooltip("Duration of dolphin lunge in seconds")]
    public float lungeDuration = 1f;

    [Tooltip("Cooldown between lunges in seconds")]
    public float lungeCooldown = 2f;

    [Tooltip("Minimum distance from player required to trigger lunge")]
    public float lungeMinDistance = 1f;

    [Tooltip("Maximum offset from exact player position for lunge target")]
    public float lungeTargetOffset = 2f;

    [Tooltip("Y-axis offset for lunge target (positive = above player, negative = below player)")]
    public float lungeTargetYOffset = 0f;

    [Tooltip("Speed multiplier for dolphin lunge")]
    public float lungeSpeedMultiplier = 3f;

    [Header("Player Mirroring Settings")]
    [Tooltip("Minimum mirroring strength")]
    public float mirroringStrengthMin = 5.0f;

    [Tooltip("Maximum mirroring strength")]
    public float mirroringStrengthMax = 15.0f;
    public float mirroringStrength =

    [Header("Flocking Settings")]
    [Tooltip("Range for detecting nearby soul creatures")]
    public float flockingRange = 15f;

    [Tooltip("Weight for cohesion (moving toward group center)")]
    [Range(0f, 1f)] public float cohesionWeight = 0.33f;

    [Tooltip("Weight for separation (avoiding crowding)")]
    [Range(0f, 1f)] public float separationWeight = 0.33f;

    [Tooltip("Weight for alignment (matching neighbors' velocity)")]
    [Range(0f, 1f)] public float alignmentWeight = 0.34f;

    [Tooltip("Minimum distance to maintain from other creatures")]
    public float separationDistance = 5f;

    [Header("Orbiting Settings")]
    [Tooltip("Radius of orbit around player")]
    public float orbitRadius = 8f;

    [Tooltip("Speed of orbital movement")]
    public float orbitSpeed = 1f;

    [Tooltip("Orbit type: 0=Horizontal, 1=Vertical, 2=Full 3D")]
    [Range(0, 2)] public int orbitType = 2;

    [Tooltip("Vertical range for 3D orbits (how far up/down from player)")]
    public float orbitVerticalRange = 5f;

    [Tooltip("Secondary orbit speed for complex 3D movement")]
    public float orbitSecondarySpeed = 0.7f;

    // Internal weighted movement variables
    private float lastLungeTime = -Mathf.Infinity;
    private bool isLunging = false;
    private float orbitAngle = 0f;
    private Vector3 currentMovementDirection = Vector3.zero;

    #endregion

    #region Player Nudging System

    [Header("Player Nudging Settings")]
    [Tooltip("Enable debug logging for player nudging")]
    public bool debugPlayerNudging = false;

    // Internal nudging variables
    [SerializeField] private bool isNudgingPlayer = false;
    [SerializeField] private float nudgeDuration = 0f;
    [SerializeField] private float nudgeStrength = 0f;
    [SerializeField] private float nudgeTimer = 0f;
    private Vector3 nudgeDirection = Vector3.zero;

    #endregion

    #region Particle Following Settings

    [Header("Particle Following Settings")]
    [Tooltip("Minimum value for minimum speed particles can move at")]
    [SerializeField] private float minMinSpeed = 1f;

    [Tooltip("Maximum value for minimum speed particles can move at")]
    [SerializeField] private float maxMinSpeed = 5f;

    [Tooltip("Current randomized minimum speed")]
    [SerializeField] private float minSpeed = 1f;

    [Tooltip("Minimum value for maximum speed particles can move at")]
    [SerializeField] private float minMaxSpeed = 10f;

    [Tooltip("Maximum value for maximum speed particles can move at")]
    [SerializeField] private float maxMaxSpeed = 20f;

    [Tooltip("Current randomized maximum speed")]
    [SerializeField] private float maxSpeed = 10f;

    [Tooltip("Rate at which particle speed interpolates to desired speed")]
    [SerializeField] private float lerpFactor = 2f;

    [Tooltip("Distance from target where particles start following")]
    [SerializeField] private float followThreshold = 1f;

    [Tooltip("Distance from target where particles stop following")]
    [SerializeField] private float stopThreshold = 0.3f;

    [Tooltip("Minimum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float minMaxDistance = 3f;

    [Tooltip("Maximum value for maximum distance at which speed scaling reaches its maximum effect")]
    [SerializeField] private float maxMaxDistance = 20f;

    [Tooltip("Current randomized maximum distance")]
    [SerializeField] private float maxDistance = 5f;

    [Header("Particle Update Rate")]
    [Tooltip("How often (in seconds) to update particle movement. Lower is smoother, higher is more performant.")]
    [SerializeField] private float particleUpdateInterval = 0.01f;
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    private float particleUpdateTimer = 0f;

    // Particle following internal variables
    private Mesh shapeMesh;
    private ParticleSystem.Particle[] particlesArray;
    private ParticleData[] particlesData;
    private float[] triangleAreas;
    private float totalArea;

    private struct ParticleData
    {
        public Vector3 TargetLocalPos;
        public float DesiredSpeed;
        public float CurrentSpeed;
        public float Timer;
        public bool IsFollowing;
        public bool initialized;
        public Vector3 Velocity;
    }

    #endregion

    [SerializeField] private float attractionSpeed = 1.0f;

    #region Wandering Settings

    [Header("Wandering Movement Settings")]
    [Tooltip("Minimum movement speed for wandering")]
    [SerializeField] private float minMoveSpeed = 1.5f;

    [Tooltip("Maximum movement speed for wandering")]
    [SerializeField] private float maxMoveSpeed = 3.0f;

    [Tooltip("Current randomized movement speed")]
    [SerializeField] private float moveSpeed = 2.0f;

    [Tooltip("Speed of rotation towards target")]
    [SerializeField] private float rotationSpeed = 1.0f;

    [Tooltip("Minimum distance to generate a new target point")]
    [SerializeField] private float minDistanceToTarget = 5.0f;

    [Tooltip("Maximum distance to generate a new target point")]
    [SerializeField] private float maxDistanceToTarget = 20.0f;

    [Tooltip("Minimum amplitude of the wave motion")]
    [SerializeField] private float minWaveAmplitude = 0.2f;

    [Tooltip("Maximum amplitude of the wave motion")]
    [SerializeField] private float maxWaveAmplitude = 0.8f;

    [Tooltip("Minimum value for maximum wave amplitude")]
    [SerializeField] private float minMaxWaveAmplitude = 1.0f;

    [Tooltip("Maximum value for maximum wave amplitude")]
    [SerializeField] private float maxMaxWaveAmplitude = 3.0f;

    [Tooltip("Minimum length of the wave motion")]
    [SerializeField] private float minWaveLength = 2.0f;

    [Tooltip("Maximum length of the wave motion")]
    [SerializeField] private float maxWaveLength = 5.0f;

    [Tooltip("Maximum attempts to generate a valid target point")]
    [SerializeField] private int maxPointGenerationAttempts = 10;

    [Tooltip("How close to get before considering target reached")]
    [SerializeField] private float targetReachedThreshold = 1.5f;

    [Header("Flight Boost")]
    [Tooltip("Amount of flight boost this soul creature provides to the player")]
    public float flightBoostValue = 6f;

    // Wandering internal variables
    private Vector3 currentTargetPoint;
    private Queue<Vector3> customTargets = new Queue<Vector3>();
    private bool hasCustomTargets = false;
    private float currentWaveAmplitude;
    private float currentWaveLength;
    private float waveOffset;

    // Timeout for wandering target
    [Tooltip("Maximum time (seconds) to pursue the same target before generating a new one")]
    [SerializeField] private float maxTargetPursuitTime = 15f;
    private float currentTargetPursuitTime = 0f;

    #endregion

    #region Audio and Gravity Control Settings

    // Audio component reference

    [Header("Gravity Control Settings")]
    [Tooltip("Number of particle collisions required to disable player gravity")]
    [SerializeField] private int particleCollisionsToDisableGravity = 10;

    [Tooltip("Time in seconds after which gravity is re-enabled if no particle collisions occur")]
    [SerializeField] private float gravityReenableDelay = 1.0f;

    [Tooltip("Enable debug logging for gravity control")]
    [SerializeField] private bool debugGravityControl = false;

    // Gravity control variables
    private int currentParticleCollisionCount = 0;
    private float lastParticleCollisionTime = -Mathf.Infinity;
    private bool isGravityDisabled = false;

    #endregion

    #region Unity Lifecycle Methods

    private void Awake()
    {
        if (enableParticleFollowing)
        {
            InitializeParticleSystem();
        }
    }

    private void OnEnable()
    {
        // Register this instance in the static list
        if (!ActiveInstances.Contains(this))
        {
            ActiveInstances.Add(this);
        }
    }

    private void OnDisable()
    {
        // Remove this instance from the static list
        ActiveInstances.Remove(this);

        // Clean up gravity control
        CleanupGravityControl();
    }

    private void Start()
    {
        // Get references from GameManager
        if (GameManager.Instance != null)
        {
            playerTransform = GameManager.Instance.player.transform;
            playerController = GameManager.Instance.player;
        }
        else
        {
            //            Debug.LogError("GameManager instance not found!");
            enabled = false;
            return;
        }

        // Get main camera reference
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogWarning("Main camera not found!");
        }

        // Debug log to verify player tag
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            //            Debug.Log($"Found player with tag 'Player': {player.name}");
        }
        else
        {
            Debug.LogWarning("No GameObject with tag 'Player' found in the scene!");
        }

        // Make sure the player from GameManager has the Player tag
        if (playerTransform != null && !playerTransform.CompareTag("Player"))
        {
            Debug.LogWarning($"Player GameObject from GameManager doesn't have the 'Player' tag! Current tag: {playerTransform.tag}");
            playerTransform.tag = "Player";
            Debug.Log("Set player tag to 'Player'");
        }

        // Initialize particle system
        ps = GetComponent<ParticleSystem>();
        if (ps == null && enableParticleFollowing)
        {
            Debug.LogError("No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }

        // Randomize particle system max particles
        var main = ps.main;
        int randomMaxParticles = Random.Range(minMaxParticles, maxMaxParticles + 1);
        var mainModule = main;
        mainModule.maxParticles = randomMaxParticles;

        // Randomize particle following settings
        minSpeed = Random.Range(minMinSpeed, maxMinSpeed);
        maxSpeed = Random.Range(minMaxSpeed, maxMaxSpeed);
        maxDistance = Random.Range(minMaxDistance, maxMaxDistance);

        // Initialize particle following
        if (enableParticleFollowing)
        {
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
            PrecomputeTriangleAreas();
        }

        // Randomize wandering settings
        moveSpeed = Random.Range(minMoveSpeed, maxMoveSpeed);
        maxWaveAmplitude = Random.Range(minMaxWaveAmplitude, maxMaxWaveAmplitude);

        // Initialize wandering
        if (enableWandering)
        {
            waveOffset = Random.Range(0f, 2f * Mathf.PI);
            currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
            currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
            GenerateNewTargetPoint();
        }

        // Initialize weighted movement system
        if (enableWeightedMovement)
        {
            waveOffset = Random.Range(0f, 2f * Mathf.PI);
            currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
            currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
            orbitAngle = Random.Range(0f, 2f * Mathf.PI);
            GenerateNewTargetPoint();
        }

        // Apply random color for ocean creatures
        if (isOceanCreature && ps != null)
        {
            ApplyRandomColor();
        }
    }

    private void Update()
    {
        if (playerTransform == null || playerController == null) return;

        // Update particle following
        if (enableParticleFollowing && ps != null && shapeMesh != null)
        {
            averageParticlePosition = GetAverageParticlePosition();
            UpdateParticles();
        }

        // Update movement system
        if (enableWeightedMovement)
        {
            UpdateWeightedMovement();
        }
        else
        {
            // Legacy movement system
            if (enableWandering)
            {
                UpdateWandering();
            }
        }

        // Update player nudging
        UpdatePlayerNudging();

        // Update render queue for ocean creatures
        if (isOceanCreature && useCentralizedRenderQueue && ps != null && GameManager.Instance != null)
        {
            UpdateRenderQueue();
        }

        // Check if we need to re-enable gravity
        CheckGravityReenabling();
    }

    private void CheckGravityReenabling()
    {
        // Only check if gravity is currently disabled by this soul creature
        if (!isGravityDisabled) return;

        // Check if enough time has passed since the last particle collision
        if (Time.time - lastParticleCollisionTime >= gravityReenableDelay)
        {
            // Reset collision count
            currentParticleCollisionCount = 0;

            // Re-enable gravity if the player controller exists
            if (playerController != null)
            {
                // We need to check if any other SoulCreatureLogic is currently disabling gravity
                bool otherCreatureDisablingGravity = false;

                // Check all other active SoulCreatureLogic objects using our static list
                foreach (SoulCreatureLogic creature in ActiveInstances)
                {
                    // Skip this creature
                    if (creature == this) continue;

                    // Check if this other creature is disabling gravity
                    if (creature.isGravityDisabled)
                    {
                        otherCreatureDisablingGravity = true;
                        break;
                    }
                }

                // Only re-enable gravity if no other creature is disabling it
                if (!otherCreatureDisablingGravity)
                {
                    playerController.SetGravityDisabled(false);

                    if (debugGravityControl)
                    {
                        Debug.Log($"Re-enabled gravity for player after no interactions with {gameObject.name} for {gravityReenableDelay} seconds");
                    }
                }
                else if (debugGravityControl)
                {
                    Debug.Log($"{gameObject.name} would re-enable gravity, but another soul creature is still disabling it");
                }

                // Mark this creature as no longer disabling gravity
                isGravityDisabled = false;
            }
        }
    }

    private void UpdateRenderQueue()
    {
        // Get all renderers (main and child particle systems)
        ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();

        // Register all materials with the centralized system
        foreach (ParticleSystemRenderer renderer in renderers)
        {
            foreach (Material material in renderer.materials)
            {
                if (material != null)
                {
                    GameManager.Instance.AddDynamicRenderQueueMaterial(material);
                }
            }
        }
    }

    private void OnParticleCollision(GameObject other)
    {
        if (other.CompareTag("Player"))
        {
            // Handle gravity control
            HandleGravityControl();
        }
    }

    private void HandleGravityControl()
    {
        // Update collision count and time
        currentParticleCollisionCount++;
        lastParticleCollisionTime = Time.time;

        // Check if we've reached the threshold to disable gravity
        if (!isGravityDisabled && currentParticleCollisionCount >= particleCollisionsToDisableGravity)
        {
            if (playerController != null)
            {
                playerController.SetGravityDisabled(true);
                isGravityDisabled = true;

                if (debugGravityControl)
                {
                    Debug.Log($"Disabled gravity for player due to {gameObject.name} particle interactions");
                }
            }
        }
    }

    private void OnDestroy()
    {
        // Clean up any active audio events
        StopAllCoroutines();

        // Clean up materials from the centralized render queue system
        if (isOceanCreature && useCentralizedRenderQueue && GameManager.Instance != null)
        {
            ParticleSystemRenderer[] renderers = GetComponentsInChildren<ParticleSystemRenderer>();
            foreach (ParticleSystemRenderer renderer in renderers)
            {
                foreach (Material material in renderer.materials)
                {
                    if (material != null)
                    {
                        GameManager.Instance.RemoveDynamicRenderQueueMaterial(material);
                    }
                }
            }
        }

        // Clean up gravity control
        CleanupGravityControl();
    }

    void CleanupGravityControl()
    {
        // Only do cleanup if this soul creature is currently disabling gravity
        if (isGravityDisabled && playerController != null)
        {
            // We need to check if any other SoulCreatureLogic is currently disabling gravity
            bool otherCreatureDisablingGravity = false;

            // Check all other active SoulCreatureLogic objects using our static list
            foreach (SoulCreatureLogic creature in ActiveInstances)
            {
                // Skip this creature
                if (creature == this) continue;

                // Check if this other creature is disabling gravity
                if (creature.isGravityDisabled)
                {
                    otherCreatureDisablingGravity = true;
                    break;
                }
            }

            // Only re-enable gravity if no other creature is disabling it
            if (!otherCreatureDisablingGravity)
            {
                playerController.SetGravityDisabled(false);

                if (debugGravityControl)
                {
                    Debug.Log($"Re-enabled gravity for player due to {gameObject.name} being disabled or destroyed");
                }
            }

            // Reset state
            isGravityDisabled = false;
            currentParticleCollisionCount = 0;
        }
    }

    #region ISoulCreatureBoostProvider Implementation

    float ISoulCreatureBoostProvider.GetFlightBoostValue() => flightBoostValue;

    #endregion

    #region Color Randomization

    void ApplyRandomColor()
    {
        var audio = GetComponent<SoulCreatureAudio>();
        if (audio != null)
        {
            int soundPairCount = audio.soundPairs != null ? audio.soundPairs.Count : 0;
            int soundPairIndex = audio.SelectedSoundPairIndex;
            Color color = SoulCreatureColorManager.GetColorForSoundPair(soundPairCount, soundPairIndex);
            // Apply the color to the particle system or renderer as needed
            if (ps != null)
            {
                var main = ps.main;
                main.startColor = color;
            }
        }
    }

    #endregion

    #region Particle Following Implementation

    private void InitializeParticleSystem()
    {
        ps = GetComponent<ParticleSystem>();
        if (ps.shape.shapeType != ParticleSystemShapeType.Mesh || (shapeMesh = ps.shape.mesh) == null)
        {
            enableParticleFollowing = false;
            return;
        }
        int maxParticles = ps.main.maxParticles;
        particlesArray = new ParticleSystem.Particle[maxParticles];
        particlesData = new ParticleData[maxParticles];
    }

    private void PrecomputeTriangleAreas()
    {
        if (shapeMesh == null) return;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;
        int triangleCount = tris.Length / 3;
        triangleAreas = new float[triangleCount];
        totalArea = 0f;

        for (int i = 0; i < triangleCount; i++)
        {
            Vector3 v0 = verts[tris[i * 3]];
            Vector3 v1 = verts[tris[i * 3 + 1]];
            Vector3 v2 = verts[tris[i * 3 + 2]];
            float area = 0.5f * Vector3.Cross(v1 - v0, v2 - v0).magnitude;
            triangleAreas[i] = area;
            totalArea += area;
        }
    }

    private void UpdateParticles()
    {
        particleUpdateTimer += Time.deltaTime;
        if (particleUpdateTimer < particleUpdateInterval)
            return;
        float step = particleUpdateTimer;
        particleUpdateTimer = 0f;

        int count = ps.GetParticles(particlesArray);
        if (particlesData == null || particlesData.Length != particlesArray.Length)
        {
            particlesData = new ParticleData[particlesArray.Length];
        }
        for (int i = 0; i < count; i++)
        {
            ref ParticleSystem.Particle particle = ref particlesArray[i];
            ref ParticleData data = ref particlesData[i];
            if (!data.initialized)
            {
                data = InitializeParticleData();
                data.initialized = true;
                data.Velocity = Vector3.zero;
            }
            UpdateParticleSpeed(ref data, step);
            Vector3 targetWorldPos = transform.TransformPoint(data.TargetLocalPos);
            float distance = Vector3.Distance(particle.position, targetWorldPos);
            data.IsFollowing = data.IsFollowing ? distance >= stopThreshold : distance > followThreshold;
            if (data.IsFollowing)
            {
                Vector3 toTarget = (targetWorldPos - particle.position);
                float dist = toTarget.magnitude;
                Vector3 desiredVelocity = Vector3.zero;
                if (dist > 0.001f)
                {
                    float speed = data.CurrentSpeed * Mathf.Min(dist / maxDistance, 1f);
                    if (dist >= maxDistance)
                    {
                        float speedScaleFactor = Mathf.Max(dist / maxDistance, 1);
                        speed = (data.CurrentSpeed + 10) * speedScaleFactor;
                    }
                    desiredVelocity = toTarget.normalized * speed;
                }
                data.Velocity = Vector3.Lerp(data.Velocity, desiredVelocity, lerpFactor * step);
                if (dist < stopThreshold * 2f)
                {
                    data.Velocity *= Mathf.Lerp(0.1f, 1f, dist / (stopThreshold * 2f));
                }
                particle.position += data.Velocity * step;
            }
            else
            {
                data.Velocity = Vector3.Lerp(data.Velocity, Vector3.zero, lerpFactor * step);
            }
        }
        ps.SetParticles(particlesArray, count);
    }

    private ParticleData InitializeParticleData()
    {
        Vector3 localPos = GetRandomMeshPoint();
        float speed = Random.Range(minSpeed, maxSpeed);
        return new ParticleData
        {
            TargetLocalPos = localPos,
            DesiredSpeed = speed,
            CurrentSpeed = speed,
            Timer = Random.Range(0f, 4f),
            IsFollowing = false,
            initialized = true,
            Velocity = Vector3.zero
        };
    }

    private void UpdateParticleSpeed(ref ParticleData data, float deltaTime)
    {
        data.Timer += deltaTime;
        if (data.Timer > 4f)
        {
            data.Timer -= 4f;
            data.DesiredSpeed = Random.Range(minSpeed, maxSpeed);
        }
        data.CurrentSpeed = Mathf.Lerp(data.CurrentSpeed, data.DesiredSpeed, lerpFactor * deltaTime);
    }

    private Vector3 GetRandomMeshPoint()
    {
        if (shapeMesh == null) return Vector3.zero;

        int[] tris = shapeMesh.triangles;
        Vector3[] verts = shapeMesh.vertices;

        float randomValue = Random.value * totalArea;
        int triIndex = 0;
        float cumulativeArea = 0f;

        for (int i = 0; i < triangleAreas.Length; i++)
        {
            cumulativeArea += triangleAreas[i];
            if (randomValue <= cumulativeArea)
            {
                triIndex = i * 3;
                break;
            }
        }

        Vector3 v0 = verts[tris[triIndex]];
        Vector3 v1 = verts[tris[triIndex + 1]];
        Vector3 v2 = verts[tris[triIndex + 2]];

        float u = Random.value, v = Random.value;
        if (u + v > 1) { u = 1 - u; v = 1 - v; }

        return v0 + u * (v1 - v0) + v * (v2 - v0);
    }

    #endregion

    #region Wandering Implementation

    private void UpdateWandering()
    {
        // Normal wandering behavior
        // Check if we should use a custom target point
        if (customTargets.Count > 0 && !hasCustomTargets)
        {
            currentTargetPoint = customTargets.Peek();
            hasCustomTargets = true;
            // Reset the pursuit timer when we get a new target
            currentTargetPursuitTime = 0f;
        }

        // Update the target pursuit timer
        currentTargetPursuitTime += Time.deltaTime;

        // Check if we've been pursuing the same target for too long
        if (currentTargetPursuitTime > maxTargetPursuitTime)
        {
            // Generate a new target point
            if (hasCustomTargets)
            {
                customTargets.Clear();
                hasCustomTargets = false;
            }
            GenerateNewTargetPoint();
            currentTargetPursuitTime = 0f;
        }

        // Check if we've reached the target
        CheckTargetStatus();

        // Move towards the current target
        MoveTowardsTarget();
    }

    private void CheckTargetStatus()
    {
        float distanceToTarget = Vector3.Distance(transform.position, currentTargetPoint);

        // Check if we've reached the target
        if (distanceToTarget < targetReachedThreshold)
        {
            if (hasCustomTargets)
            {
                customTargets.Dequeue();
                if (customTargets.Count == 0)
                {
                    hasCustomTargets = false;
                    GenerateNewTargetPoint();
                }
                else
                {
                    currentTargetPoint = customTargets.Peek();
                }
            }
            else
            {
                // Generate a new target point
                GenerateNewTargetPoint();

                // Generate new wave parameters
                currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
                currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
                waveOffset = Random.Range(0f, 2f * Mathf.PI);
            }
        }
    }

    private void MoveTowardsTarget()
    {
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);

        // Calculate direction to target
        Vector3 directionToTarget = (currentTargetPoint - transform.position).normalized;

        // Apply smooth rotation towards the target
        Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);
        transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * clampedDeltaTime);

        // Create the fish-like swimming motion
        Vector3 movement = transform.forward * moveSpeed * clampedDeltaTime;

        // Apply a sine wave to the up direction
        float waveTime = Time.time / currentWaveLength;
        Vector3 upDownMotion = transform.up * Mathf.Sin(waveTime * 2 * Mathf.PI + waveOffset) * currentWaveAmplitude;

        // Apply a cosine wave to the right direction (phase shifted for more natural movement)
        Vector3 leftRightMotion = transform.right * Mathf.Cos(waveTime * 2 * Mathf.PI + waveOffset + Mathf.PI / 4) * (currentWaveAmplitude * 0.7f);

        // Combine all movements
        Vector3 newPosition = transform.position + movement + (upDownMotion + leftRightMotion) * clampedDeltaTime;

        // Ensure we don't go past boundaries
        newPosition.y = Mathf.Clamp(newPosition.y, minY, maxY);

        // Apply the final position
        transform.position = newPosition;
    }

    private void GenerateNewTargetPoint()
    {
        Vector3 potentialTarget = Vector3.zero;
        bool validPointFound = false;
        int attempts = 0;

        // Try to find a valid point within our constraints
        while (!validPointFound && attempts < maxPointGenerationAttempts)
        {
            attempts++;

            // Choose a random distance within our range, but ensure it's not too close
            float minDistance = Mathf.Max(minDistanceToTarget, targetReachedThreshold);
            float targetDistance = Random.Range(minDistance, maxDistanceToTarget);

            // Limit the angle to be within 240 degrees of current forward direction
            // This prevents sharp turns by not allowing points directly behind
            float randomAngleY = Random.Range(-120f, 120f);
            float randomAngleX = Random.Range(-60f, 60f);

            // Create a potential direction
            Vector3 potentialDirection = Quaternion.Euler(randomAngleX, randomAngleY, 0) * transform.forward;

            // Calculate potential target
            potentialTarget = transform.position + potentialDirection * targetDistance;

            // Check if the target is fully within boundaries (including buffer)
            if (potentialTarget.y >= minY && potentialTarget.y <= maxY)
            {
                validPointFound = true;
            }
            // If we're near the edge, favor directions away from boundaries
            else if (attempts > maxPointGenerationAttempts / 2)
            {
                // Calculate center of valid space (independent of player position)
                Vector3 center = new Vector3(
                    transform.position.x,
                    (minY + maxY) / 2,
                    transform.position.z
                );

                // Weight direction toward vertical center if near boundaries
                Vector3 toCenter = new Vector3(
                    potentialDirection.x,
                    (center.y - transform.position.y) * 0.5f, // Only bias vertically
                    potentialDirection.z
                ).normalized;

                potentialTarget = transform.position + toCenter * targetDistance;

                // Ensure it's within bounds before accepting
                potentialTarget.y = Mathf.Clamp(potentialTarget.y, minY, maxY);

                validPointFound = true;
            }
        }

        // If we couldn't find a valid point after all attempts, clamp the last attempt to boundaries
        if (!validPointFound)
        {
            potentialTarget.y = Mathf.Clamp(potentialTarget.y, minY, maxY);
        }

        // Assign the new target
        currentTargetPoint = potentialTarget;
    }

    // Public method for other scripts to add custom target points
    public void AddCustomTargetPoint(Vector3 targetPoint)
    {
        // Ensure the point is within boundaries
        Vector3 boundedPoint = new Vector3(
            targetPoint.x,
            Mathf.Clamp(targetPoint.y, minY, maxY),
            targetPoint.z
        );

        // Add the point to the queue
        customTargets.Enqueue(boundedPoint);
    }

    // Add multiple custom target points
    public void AddCustomTargetPoints(Vector3[] targetPoints)
    {
        foreach (Vector3 point in targetPoints)
        {
            AddCustomTargetPoint(point);
        }
    }

    // Clear all custom target points (useful if you need to cancel the current path)
    public void ClearCustomTargetPoints()
    {
        customTargets.Clear();
        hasCustomTargets = false;
        GenerateNewTargetPoint();
    }

    #endregion



    private Vector3 GetAverageParticlePosition()
    {
        if (ps == null) return transform.position;
        int count = ps.particleCount;
        if (count == 0) return transform.position;
        if (particlesArray == null || particlesArray.Length < count)
            particlesArray = new ParticleSystem.Particle[ps.main.maxParticles];
        int actualCount = ps.GetParticles(particlesArray);
        Vector3 sum = Vector3.zero;
        for (int i = 0; i < actualCount; i++)
            sum += particlesArray[i].position;
        return sum / Mathf.Max(1, actualCount);
    }

    #endregion

    #region Weighted Movement System Implementation

    private void UpdateWeightedMovement()
    {
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        Vector3 finalMovement = Vector3.zero;

        // Calculate each behavior's contribution
        if (waypointWeight > 0f)
        {
            finalMovement += CalculateWaypointMovement() * waypointWeight;
        }

        if (playerMirroringWeight > 0f)
        {
            finalMovement += CalculatePlayerMirroringMovement() * playerMirroringWeight;
        }

        if (playerAttractionWeight > 0f)
        {
            finalMovement += CalculatePlayerAttractionMovement() * playerAttractionWeight;
        }

        // Handle dolphin lunge separately to avoid speed clamping
        Vector3 dolphinLungeMovement = Vector3.zero;
        if (dolphinLungeWeight > 0f)
        {
            dolphinLungeMovement = CalculateDolphinLungeMovement() * dolphinLungeWeight;
        }

        if (flockingWeight > 0f)
        {
            finalMovement += CalculateFlockingMovement() * flockingWeight;
        }

        if (orbitingWeight > 0f)
        {
            finalMovement += CalculateOrbitingMovement() * orbitingWeight;
        }

        // Store current movement direction for nudging system (include dolphin lunge)
        Vector3 totalMovement = finalMovement + dolphinLungeMovement;
        currentMovementDirection = totalMovement.normalized;

        // Apply the final movement
        if (totalMovement.sqrMagnitude > 0.0001f)
        {
            // Apply dolphin lunge movement directly without deltaTime multiplication to preserve speed
            if (dolphinLungeMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += dolphinLungeMovement * clampedDeltaTime;
            }

            // Apply other movements normally
            if (finalMovement.sqrMagnitude > 0.0001f)
            {
                transform.position += finalMovement * clampedDeltaTime;
            }

            // Ensure we don't go past boundaries
            Vector3 clampedPosition = transform.position;
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, minY, maxY);
            transform.position = clampedPosition;

            // Apply smooth rotation towards movement direction
            if (totalMovement.sqrMagnitude > 0.01f)
            {
                Quaternion targetRotation = Quaternion.LookRotation(totalMovement.normalized);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, rotationSpeed * clampedDeltaTime);
            }
        }
    }

    private Vector3 CalculateWaypointMovement()
    {
        // Use existing wandering logic but return movement vector instead of applying directly
        UpdateWanderingTarget();

        Vector3 directionToTarget = (currentTargetPoint - transform.position).normalized;
        Vector3 movement = directionToTarget * moveSpeed;

        // Add wave motion for natural swimming
        float waveTime = Time.time / currentWaveLength;
        Vector3 upDownMotion = Vector3.up * Mathf.Sin(waveTime * 2 * Mathf.PI + waveOffset) * currentWaveAmplitude;
        Vector3 leftRightMotion = Vector3.right * Mathf.Cos(waveTime * 2 * Mathf.PI + waveOffset + Mathf.PI / 4) * (currentWaveAmplitude * 0.7f);

        return movement + upDownMotion + leftRightMotion;
    }

    private Vector3 CalculatePlayerMirroringMovement()
    {
        // Simple, direct player movement mirroring
        Vector3 playerMoveDir = playerController.lastMoveDirection3D;
        if (playerMoveDir.sqrMagnitude <= 0.0001f) return Vector3.zero;

        // Use the dedicated mirroring strength settings
        mirroringStrength = Random.Range(mirroringStrengthMin, mirroringStrengthMax);

        return playerMoveDir.normalized * mirroringStrength;
    }

    private Vector3 CalculatePlayerAttractionMovement()
    {
        Vector3 attractionDirection = (playerTransform.position - transform.position).normalized;
        return attractionDirection * attractionSpeed;
    }

    private Vector3 CalculateDolphinLungeMovement()
    {
        float currentTime = Time.time;

        // Check distance to player
        float distanceToPlayer = Vector3.Distance(transform.position, playerTransform.position);

        // Only lunge if player is far enough away
        if (distanceToPlayer < lungeMinDistance)
        {
            return Vector3.zero;
        }

        // Check if we should start a new lunge
        if (!isLunging && currentTime - lastLungeTime >= lungeCooldown)
        {
            // Start new lunge
            isLunging = true;
            lastLungeTime = currentTime;
        }

        // Execute lunge if active
        if (isLunging)
        {
            if (currentTime - lastLungeTime < lungeDuration)
            {
                // Always target the player's current position with optional offset
                Vector3 offset = Random.insideUnitSphere * lungeTargetOffset;
                offset.y *= 0.5f; // Reduce random vertical offset
                Vector3 currentLungeTarget = playerTransform.position + offset;

                // Apply the Y offset setting
                currentLungeTarget.y += lungeTargetYOffset;

                // Ensure target is within boundaries
                currentLungeTarget.y = Mathf.Clamp(currentLungeTarget.y, minY, maxY);

                Vector3 lungeDirection = (currentLungeTarget - transform.position).normalized;
                return lungeDirection * moveSpeed * lungeSpeedMultiplier;
            }
            else
            {
                // End lunge
                isLunging = false;
            }
        }

        return Vector3.zero;
    }

    private Vector3 CalculateFlockingMovement()
    {
        Vector3 cohesion = Vector3.zero;
        Vector3 separation = Vector3.zero;
        Vector3 alignment = Vector3.zero;
        int neighborCount = 0;

        // Get all active soul creatures
        var allCreatures = GetAllActiveInstances();

        foreach (var creature in allCreatures)
        {
            if (creature == this || creature == null) continue;

            float distance = Vector3.Distance(transform.position, creature.transform.position);
            if (distance <= flockingRange)
            {
                neighborCount++;

                // Cohesion: move toward group center
                cohesion += creature.transform.position;

                // Separation: avoid crowding
                if (distance < separationDistance && distance > 0.1f)
                {
                    Vector3 separationForce = (transform.position - creature.transform.position).normalized / distance;
                    separation += separationForce;
                }

                // Alignment: match neighbors' velocity
                alignment += creature.currentMovementDirection;
            }
        }

        Vector3 flockingForce = Vector3.zero;

        if (neighborCount > 0)
        {
            // Cohesion
            cohesion = (cohesion / neighborCount - transform.position).normalized * moveSpeed * cohesionWeight;

            // Alignment
            alignment = (alignment / neighborCount).normalized * moveSpeed * alignmentWeight;

            // Combine forces
            flockingForce = cohesion + alignment;
        }

        // Add separation (calculated separately to avoid division issues)
        flockingForce += separation.normalized * moveSpeed * separationWeight;

        return flockingForce;
    }

    private Vector3 CalculateOrbitingMovement()
    {
        // Update orbit angle
        orbitAngle += orbitSpeed * Time.deltaTime;
        if (orbitAngle > 2 * Mathf.PI) orbitAngle -= 2 * Mathf.PI;

        // Calculate orbit position based on orbit type
        Vector3 orbitCenter = playerTransform.position;
        Vector3 orbitPosition;

        switch (orbitType)
        {
            case 0: // Horizontal orbit (XZ plane)
                {
                    float x = Mathf.Cos(orbitAngle) * orbitRadius;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;
                    orbitPosition = orbitCenter + new Vector3(x, 0, z);
                }
                break;

            case 1: // Vertical orbit (YZ plane or YX plane)
                {
                    float y = Mathf.Cos(orbitAngle) * orbitVerticalRange;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;
                    orbitPosition = orbitCenter + new Vector3(0, y, z);
                }
                break;

            case 2: // Full 3D orbit
            default:
                {
                    // Primary orbit in XZ plane
                    float x = Mathf.Cos(orbitAngle) * orbitRadius;
                    float z = Mathf.Sin(orbitAngle) * orbitRadius;

                    // Secondary orbit for Y movement with different frequency
                    float secondaryAngle = orbitAngle * orbitSecondarySpeed;
                    float y = Mathf.Sin(secondaryAngle) * orbitVerticalRange;

                    orbitPosition = orbitCenter + new Vector3(x, y, z);
                }
                break;
        }

        // Ensure orbit position is within boundaries
        orbitPosition.y = Mathf.Clamp(orbitPosition.y, minY, maxY);

        // Calculate movement toward orbit position
        Vector3 toOrbitPosition = (orbitPosition - transform.position).normalized;
        return toOrbitPosition * moveSpeed;
    }

    private void UpdateWanderingTarget()
    {
        // Update the target pursuit timer
        currentTargetPursuitTime += Time.deltaTime;

        // Check if we've been pursuing the same target for too long
        if (currentTargetPursuitTime > maxTargetPursuitTime)
        {
            if (hasCustomTargets)
            {
                customTargets.Clear();
                hasCustomTargets = false;
            }
            GenerateNewTargetPoint();
            currentTargetPursuitTime = 0f;
        }

        // Check if we should use a custom target point
        if (customTargets.Count > 0 && !hasCustomTargets)
        {
            currentTargetPoint = customTargets.Peek();
            hasCustomTargets = true;
            currentTargetPursuitTime = 0f;
        }

        // Check if we've reached the target
        float distanceToTarget = Vector3.Distance(transform.position, currentTargetPoint);
        if (distanceToTarget < targetReachedThreshold)
        {
            if (hasCustomTargets)
            {
                customTargets.Dequeue();
                if (customTargets.Count == 0)
                {
                    hasCustomTargets = false;
                    GenerateNewTargetPoint();
                }
                else
                {
                    currentTargetPoint = customTargets.Peek();
                }
            }
            else
            {
                GenerateNewTargetPoint();
                currentWaveAmplitude = Random.Range(minWaveAmplitude, maxWaveAmplitude);
                currentWaveLength = Random.Range(minWaveLength, maxWaveLength);
                waveOffset = Random.Range(0f, 2f * Mathf.PI);
            }
        }
    }

    #endregion

    #region Player Nudging System Implementation

    public void NudgePlayer(float durationSeconds, float strength)
    {
        if (playerController == null)
        {
            if (debugPlayerNudging)
                Debug.LogWarning("Cannot nudge player: PlayerController reference is null");
            return;
        }

        // Set nudging parameters
        isNudgingPlayer = true;
        nudgeDuration = durationSeconds;
        nudgeStrength = strength;
        nudgeTimer = 0f;
        nudgeDirection = currentMovementDirection;

        if (debugPlayerNudging)
            Debug.Log($"Started nudging player for {durationSeconds}s with strength {strength}");
    }

    private void UpdatePlayerNudging()
    {
        if (!isNudgingPlayer || playerController == null) return;

        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        nudgeTimer += clampedDeltaTime;

        if (nudgeTimer <= nudgeDuration)
        {
            // Apply nudge force
            Vector3 nudgeForce = nudgeDirection * nudgeStrength * clampedDeltaTime;
            playerController.externalMovementThisFrame += nudgeForce;

            if (debugPlayerNudging)
                Debug.Log($"Applying nudge force: {nudgeForce}");
        }
        else
        {
            // Apply decay after duration expires
            float decayTime = nudgeTimer - nudgeDuration;
            float decayFactor = Mathf.Exp(-decayTime * 2f); // Exponential decay

            if (decayFactor > 0.01f)
            {
                Vector3 decayForce = nudgeDirection * nudgeStrength * decayFactor * clampedDeltaTime;
                playerController.externalMovementThisFrame += decayForce;

                if (debugPlayerNudging)
                    Debug.Log($"Applying decay force: {decayForce} (factor: {decayFactor})");
            }
            else
            {
                // End nudging
                isNudgingPlayer = false;
                if (debugPlayerNudging)
                    Debug.Log("Nudging complete");
            }
        }
    }

    #endregion
}