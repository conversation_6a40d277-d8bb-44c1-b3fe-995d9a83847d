Unity Editor version:    6000.0.39f1 (15ea7ed0b100)
Branch:                  6000.0/staging
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.4 (Build 24E248)
Darwin version:          24.4.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        8192 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/UnityProjects/Soul_game
-logFile
Logs/AssetImportWorker0.log
-srvPort
58372
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/UnityProjects/Soul_game
/Users/<USER>/UnityProjects/Soul_game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8440450112]  Target information:

Player connection [8440450112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2355421789 [EditorId] 2355421789 [Version] 1048832 [Id] OSXEditor(0,Georgis-MacBook-Air.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8440450112] Host joined multi-casting on [***********:54997]...
Player connection [8440450112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 14.30 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.39f1 (15ea7ed0b100)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/UnityProjects/Soul_game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M1 (high power)
Metal devices available: 1
0: Apple M1 (high power)
Using device Apple M1 (high power)
Initializing Metal device caps: Apple M1
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56697
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Registered in 0.002752 seconds.
- Loaded All Assemblies, in  0.409 seconds
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.316 seconds
Domain Reload Profiling: 725ms
	BeginReloadAssembly (135ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (177ms)
				TypeCache.ScanAssembly (165ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (316ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (277ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (36ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (52ms)
			ProcessInitializeOnLoadAttributes (113ms)
			ProcessInitializeOnLoadMethodAttributes (73ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.718 seconds
Refreshing native plugins compatible for Editor in 3.16 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.03 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.760 seconds
Domain Reload Profiling: 1479ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (543ms)
		LoadAssemblies (357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (183ms)
				TypeCache.ScanAssembly (161ms)
			BuildScriptInfoCaches (34ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (761ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (606ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (107ms)
			ProcessInitializeOnLoadAttributes (412ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.24 seconds
Refreshing native plugins compatible for Editor in 2.22 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 212 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6158 unused Assets / (2.4 MB). Loaded Objects now: 6813.
Memory consumption went from 166.8 MB to 164.4 MB.
Total: 28.751750 ms (FindLiveObjects: 1.749292 ms CreateObjectMapping: 0.992458 ms MarkObjects: 21.647542 ms  DeleteObjects: 4.361834 ms)

========================================================================
Received Import Request.
  Time since last request: 48732.275873 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe961e05abf57b67e8be1feeb7bcc699') in 0.460114333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 22.525790 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa62a9db446ed7e89bf8cba4ff561617') in 0.03958525 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 5.428164 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f9aa444200bca0445af97bf101cb015') in 0.033184417 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 2.49 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 1.96 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.605 seconds
Domain Reload Profiling: 1368ms
	BeginReloadAssembly (275ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (106ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (421ms)
		LoadAssemblies (277ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (157ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (606ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (444ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (324ms)
			ProcessInitializeOnLoadMethodAttributes (36ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 1.89 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.9 MB). Loaded Objects now: 6862.
Memory consumption went from 169.6 MB to 165.7 MB.
Total: 6.247125 ms (FindLiveObjects: 0.392375 ms CreateObjectMapping: 0.200250 ms MarkObjects: 4.266334 ms  DeleteObjects: 1.387584 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  0.629 seconds
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.07 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.609 seconds
Domain Reload Profiling: 1241ms
	BeginReloadAssembly (197ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (378ms)
		LoadAssemblies (226ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (193ms)
			TypeCache.Refresh (23ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (158ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (474ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (349ms)
			ProcessInitializeOnLoadMethodAttributes (38ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.14 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 6864.
Memory consumption went from 163.0 MB to 160.0 MB.
Total: 7.021750 ms (FindLiveObjects: 0.433208 ms CreateObjectMapping: 0.211333 ms MarkObjects: 5.027625 ms  DeleteObjects: 1.349208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 110.586693 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20424b982154e3af96dd3dad8a55fc59') in 0.52689175 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 4.355999 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20e463f13ed66c71138726b3c14bd5bf') in 0.0543305 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 781.202693 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5880e021387e0d45100511179427099b') in 0.125820875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fc87000 may have been prematurely finalized
- Loaded All Assemblies, in  1.550 seconds
Refreshing native plugins compatible for Editor in 13.79 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 2.84 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.946 seconds
Domain Reload Profiling: 2506ms
	BeginReloadAssembly (584ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (181ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (889ms)
		LoadAssemblies (482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (529ms)
			TypeCache.Refresh (58ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (442ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (947ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (116ms)
			ProcessInitializeOnLoadAttributes (522ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 2.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.3 MB). Loaded Objects now: 6886.
Memory consumption went from 167.4 MB to 164.2 MB.
Total: 9.207334 ms (FindLiveObjects: 0.568209 ms CreateObjectMapping: 0.333916 ms MarkObjects: 6.732875 ms  DeleteObjects: 1.571917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.870 seconds
Refreshing native plugins compatible for Editor in 2.65 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 8.57 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.726 seconds
Domain Reload Profiling: 1603ms
	BeginReloadAssembly (326ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (18ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (164ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (465ms)
		LoadAssemblies (329ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (207ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (179ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (730ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (560ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (407ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.7 MB). Loaded Objects now: 6888.
Memory consumption went from 167.1 MB to 164.3 MB.
Total: 9.373292 ms (FindLiveObjects: 0.472625 ms CreateObjectMapping: 0.414083 ms MarkObjects: 6.937084 ms  DeleteObjects: 1.549084 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 93.439553 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8d9f038b46178ca74f0f4214634c415') in 0.335996917 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 65.835802 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7fb248d83c616286af0a82a2bc62b6c') in 0.031322292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.758 seconds
Refreshing native plugins compatible for Editor in 2.26 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.86 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.773 seconds
Domain Reload Profiling: 1534ms
	BeginReloadAssembly (221ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (28ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (477ms)
		LoadAssemblies (298ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (48ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (773ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (584ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (443ms)
			ProcessInitializeOnLoadMethodAttributes (39ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.97 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.2 MB). Loaded Objects now: 6910.
Memory consumption went from 171.4 MB to 169.2 MB.
Total: 13.251292 ms (FindLiveObjects: 0.669250 ms CreateObjectMapping: 0.772667 ms MarkObjects: 9.316333 ms  DeleteObjects: 2.492167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 95.504059 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af9055f1b4123701387d1e25bd49e546') in 0.342842542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.674 seconds
Refreshing native plugins compatible for Editor in 1.76 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 5.01 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.694 seconds
Domain Reload Profiling: 1371ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (376ms)
		LoadAssemblies (256ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (157ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (133ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (695ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (545ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (409ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (3.0 MB). Loaded Objects now: 6932.
Memory consumption went from 175.6 MB to 172.6 MB.
Total: 10.534375 ms (FindLiveObjects: 0.499458 ms CreateObjectMapping: 0.213000 ms MarkObjects: 7.529917 ms  DeleteObjects: 2.291166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  1.315 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 3.13 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.770 seconds
Domain Reload Profiling: 2090ms
	BeginReloadAssembly (573ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (57ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (3ms)
		CreateAndSetChildDomain (167ms)
	RebuildCommonClasses (33ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (668ms)
		LoadAssemblies (631ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (46ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (197ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (771ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (591ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (426ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Refreshing native plugins compatible for Editor in 2.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.8 MB). Loaded Objects now: 6934.
Memory consumption went from 175.2 MB to 172.4 MB.
Total: 11.997292 ms (FindLiveObjects: 0.625167 ms CreateObjectMapping: 0.467666 ms MarkObjects: 9.136375 ms  DeleteObjects: 1.766833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d1cf000 may have been prematurely finalized
- Loaded All Assemblies, in  0.757 seconds
Refreshing native plugins compatible for Editor in 2.46 ms, found 4 plugins.
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Refreshing native plugins compatible for Editor in 4.49 ms, found 4 plugins.
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.665 seconds
Domain Reload Profiling: 1426ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (22ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (102ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (446ms)
		LoadAssemblies (266ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (227ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (184ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (665ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (489ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (348ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 2.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6157 unused Assets / (2.6 MB). Loaded Objects now: 6936.
Memory consumption went from 175.2 MB to 172.6 MB.
Total: 11.617166 ms (FindLiveObjects: 0.668625 ms CreateObjectMapping: 0.316083 ms MarkObjects: 9.153250 ms  DeleteObjects: 1.478625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2479.190855 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3eb22ac3c11897cba9463f0d0b85f9e2') in 0.419464125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 247.759520 seconds.
  path: Assets/Prefabs/SoulCreature2_Ocean.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/SoulCreature2_Ocean.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6df636b2c6d114a95234cff6ea998588') in 0.046826542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

