{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 3915, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 3915, "ts": 1748526485261313, "dur": 521, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485265385, "dur": 988, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748526484210131, "dur": 7702, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748526484217838, "dur": 66283, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748526484284131, "dur": 51140, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485266377, "dur": 32, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484208345, "dur": 18290, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484226642, "dur": 1022884, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484228521, "dur": 8809, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484237406, "dur": 2493, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484239905, "dur": 23232, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484263154, "dur": 951, "ph": "X", "name": "ProcessMessages 4084", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484264110, "dur": 2780, "ph": "X", "name": "ReadAsync 4084", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484266914, "dur": 16, "ph": "X", "name": "ProcessMessages 8110", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484266943, "dur": 188, "ph": "X", "name": "ReadAsync 8110", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267400, "dur": 15, "ph": "X", "name": "ProcessMessages 8109", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267428, "dur": 223, "ph": "X", "name": "ReadAsync 8109", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267711, "dur": 9, "ph": "X", "name": "ProcessMessages 4451", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267726, "dur": 129, "ph": "X", "name": "ReadAsync 4451", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267890, "dur": 33, "ph": "X", "name": "ProcessMessages 2372", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484267947, "dur": 93, "ph": "X", "name": "ReadAsync 2372", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268043, "dur": 4, "ph": "X", "name": "ProcessMessages 1872", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268048, "dur": 61, "ph": "X", "name": "ReadAsync 1872", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268116, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268119, "dur": 109, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268245, "dur": 2, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268248, "dur": 103, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268358, "dur": 2, "ph": "X", "name": "ProcessMessages 909", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268364, "dur": 80, "ph": "X", "name": "ReadAsync 909", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268447, "dur": 3, "ph": "X", "name": "ProcessMessages 1911", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268451, "dur": 172, "ph": "X", "name": "ReadAsync 1911", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268680, "dur": 8, "ph": "X", "name": "ProcessMessages 3722", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268700, "dur": 127, "ph": "X", "name": "ReadAsync 3722", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268830, "dur": 22, "ph": "X", "name": "ProcessMessages 3262", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484268854, "dur": 319, "ph": "X", "name": "ReadAsync 3262", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269179, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269182, "dur": 514, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269709, "dur": 3, "ph": "X", "name": "ProcessMessages 1070", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269720, "dur": 78, "ph": "X", "name": "ReadAsync 1070", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269813, "dur": 3, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269819, "dur": 110, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269941, "dur": 2, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484269946, "dur": 106, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270058, "dur": 2, "ph": "X", "name": "ProcessMessages 739", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270063, "dur": 81, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270150, "dur": 3, "ph": "X", "name": "ProcessMessages 1339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270155, "dur": 174, "ph": "X", "name": "ReadAsync 1339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270333, "dur": 3, "ph": "X", "name": "ProcessMessages 1143", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270338, "dur": 219, "ph": "X", "name": "ReadAsync 1143", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270584, "dur": 2, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270588, "dur": 298, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270895, "dur": 5, "ph": "X", "name": "ProcessMessages 2528", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484270915, "dur": 1394, "ph": "X", "name": "ReadAsync 2528", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484272315, "dur": 3, "ph": "X", "name": "ProcessMessages 2124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484272320, "dur": 2082, "ph": "X", "name": "ReadAsync 2124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274424, "dur": 21, "ph": "X", "name": "ProcessMessages 8133", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274450, "dur": 161, "ph": "X", "name": "ReadAsync 8133", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274614, "dur": 3, "ph": "X", "name": "ProcessMessages 835", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274629, "dur": 175, "ph": "X", "name": "ReadAsync 835", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274807, "dur": 2, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484274811, "dur": 84, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275059, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275062, "dur": 193, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275402, "dur": 4, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275408, "dur": 159, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275570, "dur": 7, "ph": "X", "name": "ProcessMessages 3664", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484275578, "dur": 686, "ph": "X", "name": "ReadAsync 3664", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276271, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276276, "dur": 123, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276402, "dur": 8, "ph": "X", "name": "ProcessMessages 4519", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276411, "dur": 73, "ph": "X", "name": "ReadAsync 4519", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276488, "dur": 2, "ph": "X", "name": "ProcessMessages 1701", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276492, "dur": 288, "ph": "X", "name": "ReadAsync 1701", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276783, "dur": 4, "ph": "X", "name": "ProcessMessages 1900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276788, "dur": 60, "ph": "X", "name": "ReadAsync 1900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276850, "dur": 2, "ph": "X", "name": "ProcessMessages 1238", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276854, "dur": 51, "ph": "X", "name": "ReadAsync 1238", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276907, "dur": 2, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276911, "dur": 73, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276986, "dur": 2, "ph": "X", "name": "ProcessMessages 1735", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484276989, "dur": 68, "ph": "X", "name": "ReadAsync 1735", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277059, "dur": 1, "ph": "X", "name": "ProcessMessages 719", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277062, "dur": 200, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277266, "dur": 15, "ph": "X", "name": "ProcessMessages 3260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277283, "dur": 61, "ph": "X", "name": "ReadAsync 3260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277347, "dur": 2, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277351, "dur": 45, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277413, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277416, "dur": 69, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277487, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277490, "dur": 41, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277533, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277535, "dur": 97, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277636, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277682, "dur": 103, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277789, "dur": 3, "ph": "X", "name": "ProcessMessages 1327", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277793, "dur": 98, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277893, "dur": 2, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277898, "dur": 76, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277977, "dur": 1, "ph": "X", "name": "ProcessMessages 763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484277980, "dur": 82, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278064, "dur": 2, "ph": "X", "name": "ProcessMessages 1340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278068, "dur": 53, "ph": "X", "name": "ReadAsync 1340", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278123, "dur": 3, "ph": "X", "name": "ProcessMessages 1389", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278127, "dur": 146, "ph": "X", "name": "ReadAsync 1389", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278275, "dur": 2, "ph": "X", "name": "ProcessMessages 1208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278279, "dur": 62, "ph": "X", "name": "ReadAsync 1208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278343, "dur": 12, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278360, "dur": 74, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278452, "dur": 1, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278454, "dur": 43, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278498, "dur": 5, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278505, "dur": 31, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278539, "dur": 184, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278725, "dur": 3, "ph": "X", "name": "ProcessMessages 1698", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278730, "dur": 73, "ph": "X", "name": "ReadAsync 1698", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278861, "dur": 4, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278869, "dur": 78, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278950, "dur": 4, "ph": "X", "name": "ProcessMessages 2286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484278955, "dur": 110, "ph": "X", "name": "ReadAsync 2286", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279068, "dur": 3, "ph": "X", "name": "ProcessMessages 1774", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279072, "dur": 58, "ph": "X", "name": "ReadAsync 1774", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279133, "dur": 62, "ph": "X", "name": "ProcessMessages 2260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279198, "dur": 104, "ph": "X", "name": "ReadAsync 2260", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279305, "dur": 4, "ph": "X", "name": "ProcessMessages 2308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279338, "dur": 84, "ph": "X", "name": "ReadAsync 2308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279465, "dur": 20, "ph": "X", "name": "ProcessMessages 3624", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279499, "dur": 146, "ph": "X", "name": "ReadAsync 3624", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279648, "dur": 8, "ph": "X", "name": "ProcessMessages 4768", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279660, "dur": 102, "ph": "X", "name": "ReadAsync 4768", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279765, "dur": 2, "ph": "X", "name": "ProcessMessages 1328", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279769, "dur": 97, "ph": "X", "name": "ReadAsync 1328", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279878, "dur": 2, "ph": "X", "name": "ProcessMessages 1165", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279882, "dur": 53, "ph": "X", "name": "ReadAsync 1165", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279937, "dur": 3, "ph": "X", "name": "ProcessMessages 1867", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484279941, "dur": 73, "ph": "X", "name": "ReadAsync 1867", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280018, "dur": 3, "ph": "X", "name": "ProcessMessages 1925", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280022, "dur": 68, "ph": "X", "name": "ReadAsync 1925", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280093, "dur": 2, "ph": "X", "name": "ProcessMessages 1581", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280096, "dur": 214, "ph": "X", "name": "ReadAsync 1581", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280312, "dur": 5, "ph": "X", "name": "ProcessMessages 2013", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280319, "dur": 53, "ph": "X", "name": "ReadAsync 2013", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280375, "dur": 2, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280379, "dur": 507, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484280890, "dur": 4, "ph": "X", "name": "ProcessMessages 1780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281056, "dur": 187, "ph": "X", "name": "ReadAsync 1780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281247, "dur": 9, "ph": "X", "name": "ProcessMessages 6654", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281258, "dur": 90, "ph": "X", "name": "ReadAsync 6654", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281350, "dur": 2, "ph": "X", "name": "ProcessMessages 1439", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281354, "dur": 52, "ph": "X", "name": "ReadAsync 1439", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281409, "dur": 2, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281412, "dur": 72, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281500, "dur": 2, "ph": "X", "name": "ProcessMessages 1297", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281504, "dur": 57, "ph": "X", "name": "ReadAsync 1297", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281563, "dur": 1, "ph": "X", "name": "ProcessMessages 1904", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281565, "dur": 45, "ph": "X", "name": "ReadAsync 1904", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281614, "dur": 177, "ph": "X", "name": "ReadAsync 1144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281829, "dur": 4, "ph": "X", "name": "ProcessMessages 1727", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484281834, "dur": 240, "ph": "X", "name": "ReadAsync 1727", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282077, "dur": 7, "ph": "X", "name": "ProcessMessages 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282085, "dur": 114, "ph": "X", "name": "ReadAsync 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282201, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282203, "dur": 97, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282303, "dur": 355, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282662, "dur": 4, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282667, "dur": 288, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282957, "dur": 2, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484282960, "dur": 174, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283145, "dur": 21, "ph": "X", "name": "ProcessMessages 2581", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283195, "dur": 153, "ph": "X", "name": "ReadAsync 2581", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283350, "dur": 1, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283352, "dur": 99, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283454, "dur": 2, "ph": "X", "name": "ProcessMessages 751", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283458, "dur": 53, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283513, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283515, "dur": 92, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283611, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484283613, "dur": 948, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484284564, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484284566, "dur": 418, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484284987, "dur": 68, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484285068, "dur": 4, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484285078, "dur": 468, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484285550, "dur": 2, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484285554, "dur": 899, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484286456, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484286468, "dur": 182, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484286654, "dur": 404, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287060, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287062, "dur": 284, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287349, "dur": 2, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287354, "dur": 266, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287629, "dur": 2, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484287633, "dur": 438, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484288073, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484288075, "dur": 794, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484289834, "dur": 23, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484289859, "dur": 288, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290150, "dur": 53, "ph": "X", "name": "ProcessMessages 2207", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290207, "dur": 115, "ph": "X", "name": "ReadAsync 2207", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290326, "dur": 2, "ph": "X", "name": "ProcessMessages 969", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290330, "dur": 341, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290674, "dur": 2, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484290678, "dur": 392, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484291072, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484291075, "dur": 295, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484291374, "dur": 293, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484291671, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484291674, "dur": 332, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292019, "dur": 2, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292022, "dur": 226, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292251, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292253, "dur": 260, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292517, "dur": 3, "ph": "X", "name": "ProcessMessages 777", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292521, "dur": 449, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292985, "dur": 1, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484292988, "dur": 98, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293088, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293091, "dur": 187, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293281, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293283, "dur": 592, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293878, "dur": 2, "ph": "X", "name": "ProcessMessages 1240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293882, "dur": 54, "ph": "X", "name": "ReadAsync 1240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293938, "dur": 2, "ph": "X", "name": "ProcessMessages 711", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484293942, "dur": 66, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484294011, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484294027, "dur": 617, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484294647, "dur": 1, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484294650, "dur": 436, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484295090, "dur": 195, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484295286, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484295288, "dur": 459, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484295750, "dur": 256, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296009, "dur": 226, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296237, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296239, "dur": 196, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296437, "dur": 1, "ph": "X", "name": "ProcessMessages 1846", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296439, "dur": 227, "ph": "X", "name": "ReadAsync 1846", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296668, "dur": 10, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296679, "dur": 173, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484296855, "dur": 216, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297074, "dur": 320, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297396, "dur": 2, "ph": "X", "name": "ProcessMessages 2243", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297400, "dur": 48, "ph": "X", "name": "ReadAsync 2243", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297451, "dur": 262, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297716, "dur": 54, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484297772, "dur": 281, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298056, "dur": 236, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298295, "dur": 223, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298525, "dur": 1, "ph": "X", "name": "ProcessMessages 1294", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298558, "dur": 282, "ph": "X", "name": "ReadAsync 1294", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298847, "dur": 1, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484298848, "dur": 311, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484299161, "dur": 151, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484299315, "dur": 194, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484299512, "dur": 250, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484299767, "dur": 14, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484299783, "dur": 267, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300053, "dur": 73, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300129, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300132, "dur": 154, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300302, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300306, "dur": 426, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300750, "dur": 3, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484300754, "dur": 271, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484301029, "dur": 1, "ph": "X", "name": "ProcessMessages 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484301031, "dur": 577, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484301612, "dur": 132, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484301747, "dur": 576, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484302327, "dur": 632, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484302962, "dur": 814, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484303779, "dur": 211, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484303993, "dur": 2055, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484306054, "dur": 2, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484306058, "dur": 858, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484306923, "dur": 15, "ph": "X", "name": "ProcessMessages 912", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484306957, "dur": 816, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484307852, "dur": 2, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484307856, "dur": 180, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484308039, "dur": 147, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484308190, "dur": 15, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484308207, "dur": 478, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484308686, "dur": 1, "ph": "X", "name": "ProcessMessages 2205", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484308689, "dur": 712, "ph": "X", "name": "ReadAsync 2205", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484309404, "dur": 620, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484310035, "dur": 1, "ph": "X", "name": "ProcessMessages 951", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484310037, "dur": 231, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484310271, "dur": 800, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484311075, "dur": 710, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484311789, "dur": 5, "ph": "X", "name": "ProcessMessages 2089", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484311796, "dur": 55, "ph": "X", "name": "ReadAsync 2089", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484311854, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484311857, "dur": 312, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484312172, "dur": 3, "ph": "X", "name": "ProcessMessages 1261", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484312177, "dur": 111, "ph": "X", "name": "ReadAsync 1261", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484312292, "dur": 3, "ph": "X", "name": "ProcessMessages 1103", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484312296, "dur": 1330, "ph": "X", "name": "ReadAsync 1103", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484313671, "dur": 5, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484313678, "dur": 914, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484314627, "dur": 3, "ph": "X", "name": "ProcessMessages 2577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484314631, "dur": 722, "ph": "X", "name": "ReadAsync 2577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484315355, "dur": 1, "ph": "X", "name": "ProcessMessages 2367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484315358, "dur": 1000, "ph": "X", "name": "ReadAsync 2367", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316367, "dur": 3, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316372, "dur": 248, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316624, "dur": 4, "ph": "X", "name": "ProcessMessages 1615", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316631, "dur": 236, "ph": "X", "name": "ReadAsync 1615", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316869, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484316871, "dur": 2285, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484319318, "dur": 10, "ph": "X", "name": "ProcessMessages 2460", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484319330, "dur": 6229, "ph": "X", "name": "ReadAsync 2460", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484325565, "dur": 685, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484326871, "dur": 1757, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484328649, "dur": 4, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484328654, "dur": 2447, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484331108, "dur": 13, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484331125, "dur": 1386, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332515, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332518, "dur": 179, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332699, "dur": 15, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332722, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332926, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484332974, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333182, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333184, "dur": 286, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333473, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333595, "dur": 182, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333783, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333788, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484333851, "dur": 479, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484334333, "dur": 117, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484334453, "dur": 239, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484334695, "dur": 247, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484334944, "dur": 38, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484334988, "dur": 167, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484335158, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484335160, "dur": 741, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484335903, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484335905, "dur": 641, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484336548, "dur": 827, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484337379, "dur": 1024, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484338406, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484338415, "dur": 265, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484338683, "dur": 552, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484339238, "dur": 294, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484339535, "dur": 1454, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341000, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341029, "dur": 594, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341627, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341630, "dur": 257, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341944, "dur": 3, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484341949, "dur": 274, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342253, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342255, "dur": 418, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342677, "dur": 37, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342717, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342766, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484342869, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343129, "dur": 376, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343511, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343515, "dur": 149, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343668, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343672, "dur": 227, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484343902, "dur": 134, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344038, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344441, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344446, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344542, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344611, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344614, "dur": 254, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484344871, "dur": 668, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484345542, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484345544, "dur": 305, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484345850, "dur": 40, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484346087, "dur": 188, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484346279, "dur": 154, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484346436, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484346439, "dur": 605, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347049, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347071, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347123, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347125, "dur": 143, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347281, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347285, "dur": 307, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347594, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347596, "dur": 182, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484347780, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348026, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348056, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348117, "dur": 352, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348471, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348478, "dur": 237, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348718, "dur": 161, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484348882, "dur": 265, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349150, "dur": 200, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349352, "dur": 167, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349527, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349532, "dur": 167, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349702, "dur": 99, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349804, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349837, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484349980, "dur": 22, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484350003, "dur": 92, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484350115, "dur": 1002, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351119, "dur": 6, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351127, "dur": 376, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351506, "dur": 206, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351714, "dur": 6, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351721, "dur": 39, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484351762, "dur": 619, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484352382, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484352384, "dur": 2640, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484355029, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484355033, "dur": 249, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484355285, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484355342, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484355432, "dur": 1405, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484356845, "dur": 80, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484356928, "dur": 133, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484357064, "dur": 175, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484357251, "dur": 251, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484357516, "dur": 17080, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484374619, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484374625, "dur": 705, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484375334, "dur": 7168, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382516, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382527, "dur": 150, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382695, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382697, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382874, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484382876, "dur": 3706, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484386588, "dur": 13, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484386603, "dur": 11721, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484398336, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484398342, "dur": 448, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484398793, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484398795, "dur": 208, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484399008, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484399010, "dur": 3023, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484402037, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484402039, "dur": 9334, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484411379, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484411382, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484411473, "dur": 513, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484411990, "dur": 306, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484412299, "dur": 232, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484412534, "dur": 540, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484413076, "dur": 948, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484414027, "dur": 116, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484414151, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484414154, "dur": 610, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484414766, "dur": 248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484415016, "dur": 598, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484415617, "dur": 226, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484415846, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484415984, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484416064, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484416246, "dur": 357, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484416605, "dur": 230, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484416837, "dur": 127, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484416967, "dur": 495, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484417500, "dur": 1030, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484418533, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484418535, "dur": 1898, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484420438, "dur": 27, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484420466, "dur": 3677, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484424155, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484424188, "dur": 415, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484424607, "dur": 24, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484424633, "dur": 1593, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484426231, "dur": 129, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484426362, "dur": 220, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484426586, "dur": 241, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484426829, "dur": 176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484427023, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484427027, "dur": 623, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484427652, "dur": 1059, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484428717, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484428720, "dur": 1373, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484430097, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484430099, "dur": 1077, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484431183, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484431187, "dur": 1165, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484432357, "dur": 1130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484433490, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484433492, "dur": 1871, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435372, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435378, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435488, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435490, "dur": 327, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435821, "dur": 136, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484435960, "dur": 180, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484436143, "dur": 1424, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484437572, "dur": 1313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484438892, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484438895, "dur": 371, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484439269, "dur": 1361, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484440635, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484440637, "dur": 4285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484444927, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484444930, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484445136, "dur": 561, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484445701, "dur": 627, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484446331, "dur": 737, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447072, "dur": 171, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447250, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447254, "dur": 239, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447496, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447498, "dur": 286, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484447786, "dur": 762, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484448551, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484448634, "dur": 247, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484448885, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484448889, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449037, "dur": 159, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449198, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449343, "dur": 167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449512, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449514, "dur": 80, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449603, "dur": 359, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484449964, "dur": 232, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484450199, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484450202, "dur": 477, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484450682, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484450684, "dur": 830, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484451518, "dur": 346, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484451867, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484451869, "dur": 394, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452277, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452305, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452407, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452588, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452591, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484452736, "dur": 350, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484453088, "dur": 2702, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484455794, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484455810, "dur": 335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484456149, "dur": 106, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484456258, "dur": 253, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484456513, "dur": 1052, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484457570, "dur": 645, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458218, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458301, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458481, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458518, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458611, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458705, "dur": 277, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484458985, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484459148, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484459153, "dur": 252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484459408, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484459520, "dur": 1103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484460625, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484460771, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484460929, "dur": 1335, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484462268, "dur": 694, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484462970, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484462975, "dur": 447, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484463425, "dur": 299, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484463730, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484463734, "dur": 425, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484464162, "dur": 210, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484464375, "dur": 339, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484464724, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484464828, "dur": 377, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484465210, "dur": 14, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484465226, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484465367, "dur": 351, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484465720, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484465722, "dur": 364, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466088, "dur": 241, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466335, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466339, "dur": 204, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466546, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466652, "dur": 96, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466751, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466783, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484466904, "dur": 319, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484467237, "dur": 275, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484467514, "dur": 339, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484467855, "dur": 386, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484468243, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484468245, "dur": 94189, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562442, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562452, "dur": 65, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562520, "dur": 98, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562654, "dur": 48, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562703, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562782, "dur": 151, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562935, "dur": 7, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484562942, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484563022, "dur": 1427, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484564452, "dur": 3036, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484567493, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484567495, "dur": 1397, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484568900, "dur": 18, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484568921, "dur": 383, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484569307, "dur": 851, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484570161, "dur": 1173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484571337, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484571339, "dur": 1167, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484572511, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484572513, "dur": 1423, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484573944, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484573949, "dur": 1030, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484574982, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484574984, "dur": 1146, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484576135, "dur": 780, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484576918, "dur": 506, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484577427, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484577551, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484577553, "dur": 1329, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484578886, "dur": 427, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484579317, "dur": 1469, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484580788, "dur": 234, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484581029, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484581032, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484581252, "dur": 822, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484582084, "dur": 1429, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484583516, "dur": 912, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484584431, "dur": 1519, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484585952, "dur": 8, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484585961, "dur": 681, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484586645, "dur": 454, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484587106, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484587109, "dur": 426, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484587538, "dur": 1740, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484589284, "dur": 2317, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484591604, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484591606, "dur": 1259, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484592868, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484593001, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484593004, "dur": 350, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484593357, "dur": 772, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594131, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594204, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594318, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594319, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594411, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594418, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594469, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594511, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594573, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594651, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594753, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594881, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484594971, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595000, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595059, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595084, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595184, "dur": 105, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595291, "dur": 93, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595412, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595435, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595502, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595665, "dur": 137, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595804, "dur": 154, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484595960, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596019, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596047, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596124, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596150, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596180, "dur": 108, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596291, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596324, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596387, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596441, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596504, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596506, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596538, "dur": 59, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596598, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596606, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596633, "dur": 110, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596745, "dur": 157, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596904, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596948, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484596968, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597005, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597011, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597072, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597139, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597215, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597305, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597370, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597401, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597433, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597577, "dur": 107, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597687, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597749, "dur": 75, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484597826, "dur": 302, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484598134, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484598139, "dur": 377, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484598525, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484598603, "dur": 363442, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484962074, "dur": 58, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484962134, "dur": 1317, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484963455, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484963457, "dur": 1602, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484965068, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526484965070, "dur": 153658, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485118742, "dur": 39, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485118782, "dur": 3268, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485122060, "dur": 6, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485122068, "dur": 47445, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169519, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169521, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169576, "dur": 79, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169658, "dur": 43, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169704, "dur": 72, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169788, "dur": 26, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485169814, "dur": 4093, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485173916, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485173920, "dur": 606, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485174539, "dur": 32, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485174573, "dur": 65888, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240466, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240469, "dur": 38, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240509, "dur": 37, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240548, "dur": 46, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240596, "dur": 24, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240621, "dur": 21, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485240643, "dur": 2906, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485243552, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485243555, "dur": 582, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485244141, "dur": 25, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485244168, "dur": 235, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485244416, "dur": 310, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748526485244728, "dur": 4691, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485266411, "dur": 1516, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 8589934592, "ts": 1748526484205034, "dur": 130330, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748526484335367, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748526484335372, "dur": 5739, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485267928, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 4294967296, "ts": 1748526484124439, "dur": 1126697, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748526484131074, "dur": 68856, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748526485251229, "dur": 7696, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748526485257213, "dur": 39, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748526485259007, "dur": 11, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485267956, "dur": 10, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748526484218886, "dur": 6809, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526484225709, "dur": 36724, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526484262490, "dur": 295, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748526484262786, "dur": 579, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526484264480, "dur": 2995, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484267536, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484268128, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484268700, "dur": 115, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484271269, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484271724, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1748526484271856, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484272411, "dur": 750, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484273174, "dur": 1395, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748526484275036, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748526484275241, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484275481, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484276534, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484276778, "dur": 213, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484277775, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484280470, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484281030, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484281949, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484282490, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748526484282552, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484283888, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484284147, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748526484293788, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484296814, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526484297005, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484299801, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526484305097, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748526484312349, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526484315128, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1748526484316749, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526484317023, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1748526484317290, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748526484319310, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526484319548, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748526484263378, "dur": 57636, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526484321023, "dur": 923822, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526485244976, "dur": 56, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748526485245060, "dur": 997, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748526484262923, "dur": 58674, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484321600, "dur": 2862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748526484324508, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484324601, "dur": 1207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484325815, "dur": 489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484326304, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484326377, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484327154, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484327233, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484327352, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484327563, "dur": 1403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484328966, "dur": 1440, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484330411, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484331067, "dur": 747, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484331821, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484332432, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484333026, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484333578, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484333775, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484334243, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484334380, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484334816, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484334879, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484335269, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484335471, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484335882, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484336380, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484336701, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484336867, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484338116, "dur": 1108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484339234, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748526484340243, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484340590, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484342777, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484342845, "dur": 39962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484382808, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484383026, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484383188, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484383319, "dur": 2300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484385630, "dur": 12787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484398418, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484398736, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484398865, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484399033, "dur": 3639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484402715, "dur": 9036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484411751, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484412029, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484412109, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484412184, "dur": 739, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484412925, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484412978, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484413049, "dur": 5696, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484418746, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484419138, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484419224, "dur": 1245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484420471, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484420646, "dur": 15304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484435950, "dur": 704, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484436663, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.ref.dll_CB2B1FA2A63FF8D9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484436764, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484436822, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748526484438392, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484438456, "dur": 6973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484445429, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484445561, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748526484447019, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484447170, "dur": 1041, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484563002, "dur": 629, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484448574, "dur": 115077, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1748526484564772, "dur": 1962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484566735, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484566805, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484570823, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484570959, "dur": 2198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484573158, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484573320, "dur": 5364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484578723, "dur": 2929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484581652, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484581793, "dur": 4600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484586394, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484586510, "dur": 3367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484589878, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484590019, "dur": 3187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484593261, "dur": 5422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748526484598684, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484598803, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748526484599316, "dur": 645525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484262930, "dur": 61384, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484324319, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748526484324750, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484325396, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484325457, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484326343, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484327099, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484327207, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484327355, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484327574, "dur": 2869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484330444, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484330871, "dur": 912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484331783, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484332073, "dur": 804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484332878, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484333238, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484333657, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484333805, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484334291, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484334454, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484334837, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484334913, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484335282, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484335631, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484336183, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484336406, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484336809, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484336892, "dur": 1414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484338307, "dur": 1158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484339474, "dur": 1541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748526484341016, "dur": 787, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484341809, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484342155, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748526484343341, "dur": 26925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484370267, "dur": 1514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484371805, "dur": 3560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484375367, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484380162, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1748526484375873, "dur": 5268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484381141, "dur": 2657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484383799, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484385110, "dur": 1640, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Converter/RenderPipelineConverter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1748526484385110, "dur": 2805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484387915, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484389222, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484390139, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484391126, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484391969, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484393137, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484393965, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484395247, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484396443, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484397922, "dur": 1788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484399710, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484401225, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484402018, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484402873, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484403698, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484404626, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484405935, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484406751, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484407508, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484408731, "dur": 1755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484410486, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484411299, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484412342, "dur": 1234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484413596, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484416210, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484416580, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484416666, "dur": 1309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484417975, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484418031, "dur": 27920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484445951, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484446247, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484446450, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484447373, "dur": 740, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484448120, "dur": 4456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484452576, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484452870, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_C51C3847CE9500F9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484453133, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484453299, "dur": 5493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484458793, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484458917, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484461334, "dur": 2251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484463585, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484463848, "dur": 2066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484465915, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748526484465997, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484466410, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484466788, "dur": 1433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484468222, "dur": 96619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484564848, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484567037, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484567103, "dur": 2911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484570015, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484570138, "dur": 5493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484575632, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484575784, "dur": 4708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484580493, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484580583, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484584235, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484584299, "dur": 3934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484588248, "dur": 2914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484591185, "dur": 3115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484594300, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748526484594358, "dur": 4919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748526484599297, "dur": 645515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484263368, "dur": 61156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484324528, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484326035, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484326306, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484326509, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484327106, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484327218, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484327344, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484327494, "dur": 1365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484328859, "dur": 1287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484330251, "dur": 694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484330945, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484331663, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484332162, "dur": 669, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484332857, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484333489, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484333692, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484334164, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484334345, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484334652, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484334855, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484335223, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484335417, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484335844, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484336296, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484336632, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484336836, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484337488, "dur": 848, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484338372, "dur": 1514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748526484339887, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484340019, "dur": 1433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484341453, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484342119, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484342779, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484342867, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484343077, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484343386, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484343980, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484344168, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748526484344358, "dur": 802, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484345160, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484345328, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484345694, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484345757, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748526484345921, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484346426, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484346515, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484346847, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484346934, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484347659, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484347829, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748526484348005, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484348127, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484348185, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484348381, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484348457, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484348853, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484348915, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484349272, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484349568, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484350050, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484350151, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484350440, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484350643, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484350928, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484351276, "dur": 386, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484351666, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484351917, "dur": 393, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484352313, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484352472, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484352741, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484353065, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484353128, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484353213, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484353275, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484353840, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484353935, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484354019, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484354455, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484354588, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484354967, "dur": 1024, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484355995, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484356166, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484356415, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484356515, "dur": 474, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484357048, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484357259, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484357417, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484357502, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748526484357736, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484358048, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484360145, "dur": 2808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484366516, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748526484362954, "dur": 4293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484367247, "dur": 3592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484370840, "dur": 905, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Localization.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748526484371746, "dur": 3583, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748526484375749, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Identity.Core.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748526484370839, "dur": 9818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484380658, "dur": 2640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484383326, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1748526484383442, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484383597, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484385071, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484385634, "dur": 1091, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/EditorTool/PathComponentEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748526484385633, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484387629, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484389073, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484390030, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484390946, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484391854, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484392961, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484393800, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484395075, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484396239, "dur": 1538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484397778, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484399348, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484399415, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748526484399517, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484399626, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484401186, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484401995, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484402866, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484403658, "dur": 941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484404600, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484405924, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484406727, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484407485, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484408707, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484410464, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484411276, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484412350, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484412658, "dur": 1803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484414462, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484414784, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484414846, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484415433, "dur": 8276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484423710, "dur": 847, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484424610, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484426695, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484426881, "dur": 8773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484435655, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484435910, "dur": 3716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484439627, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484439816, "dur": 6877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484446694, "dur": 601, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484447306, "dur": 1866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484449172, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484449348, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_CBF917C274624EE2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484449403, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484449526, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484449623, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484449758, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484449903, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484449980, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484450040, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484450279, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_A276D0F1D4D046E3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484450457, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484450591, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484451214, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484451315, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484452408, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484452492, "dur": 6358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484458850, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484458953, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484459032, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484459100, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484459563, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484459661, "dur": 2741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484462402, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484462499, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484462890, "dur": 114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484463052, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484463185, "dur": 1262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484464448, "dur": 593, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484465047, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_FE8D1F6CE20A2409.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484465190, "dur": 1542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484466735, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484466916, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484467239, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_AD2FFB7646FD92B4.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484467353, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484468149, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748526484468241, "dur": 96531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484564775, "dur": 3410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484568186, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484568344, "dur": 3671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484572016, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484572104, "dur": 3937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484576042, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484576125, "dur": 3914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484580093, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484582796, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484582854, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484586628, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484586709, "dur": 5163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484591873, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484591945, "dur": 5704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748526484597650, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484597742, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484597841, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484597922, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484598169, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484598381, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484598507, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526484599242, "dur": 644898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748526485244192, "dur": 585, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484263374, "dur": 61411, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484324792, "dur": 914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484325731, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BA86083999E80018.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484326015, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484326307, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484326594, "dur": 562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484327156, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484327239, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484327796, "dur": 1186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484328987, "dur": 1790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484330778, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484331010, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484331778, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484332023, "dur": 948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484332971, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484333376, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484333716, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484333831, "dur": 523, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484334354, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484334560, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484334861, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484335151, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484335435, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484335776, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484336359, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484336476, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484336846, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484337381, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484338514, "dur": 1464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484340031, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484340428, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484340642, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484340949, "dur": 1138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484342088, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484342196, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484342312, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484342541, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484342864, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1748526484343428, "dur": 424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748526484343862, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484344069, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484344229, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484344331, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484344506, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484344653, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484344829, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484345059, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484345171, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484345340, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484345585, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484345734, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484345821, "dur": 487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484346311, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484346524, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484346826, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484346917, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484347018, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484347107, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484347240, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484347323, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484347662, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484347792, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484348080, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484348176, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484348253, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484348799, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484348883, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484349270, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484349427, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484349566, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484349970, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484350282, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484350492, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484350701, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484351053, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484351291, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484351692, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748526484351785, "dur": 510, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484352298, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484352392, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484352594, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484352897, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484353108, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484353204, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484353302, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484353838, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484354022, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484354172, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484354427, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484354557, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484355569, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484356040, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484356201, "dur": 316, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484356621, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484356796, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484357085, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484357298, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484357423, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484357546, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748526484357659, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484357936, "dur": 2197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484360133, "dur": 2963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484366499, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484363097, "dur": 4512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484370877, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484371795, "dur": 3518, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Data.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484367609, "dur": 7705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484375745, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Mvc.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484375316, "dur": 5694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484381011, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484383778, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484385440, "dur": 1370, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/RendererFeatures/DecalRendererFeatureEditor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748526484385088, "dur": 2202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484387290, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484388678, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484389881, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484390574, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484391668, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484392572, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484393601, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484394795, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484395864, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484397162, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484398610, "dur": 2032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484400643, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484401627, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484402462, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484403311, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484404159, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484405407, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484406393, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484407126, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484408151, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484409468, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484410913, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484411852, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484412573, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484412815, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484415441, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484415754, "dur": 1517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484417312, "dur": 7539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484424852, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484425276, "dur": 2848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484428125, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484428188, "dur": 9931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484438119, "dur": 1063, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484439221, "dur": 6728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484445950, "dur": 218, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484446331, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484446966, "dur": 1674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484448642, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484448897, "dur": 1863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484450760, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484450821, "dur": 1222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484452094, "dur": 3828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484455923, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484456250, "dur": 2586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484458836, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459073, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459229, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459313, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459432, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 4, "ts": 1748526484459498, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459613, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484459802, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484461064, "dur": 1057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484462121, "dur": 87, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484462438, "dur": 154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484462615, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484462901, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484463392, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484464012, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748526484464088, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484464882, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484465057, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484465716, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484466454, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484466792, "dur": 1432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484468225, "dur": 96624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484564859, "dur": 3804, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484568713, "dur": 6751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484575466, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484575549, "dur": 3616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484579166, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484579230, "dur": 4189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484583420, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484583492, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484585550, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484585608, "dur": 2617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484588226, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484588288, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484590565, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484590696, "dur": 2853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484593549, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526484593617, "dur": 5589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748526484599232, "dur": 575165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748526485174398, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748526485174482, "dur": 729, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1748526485175213, "dur": 69612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484263377, "dur": 61483, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484324864, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484325402, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484325459, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484325666, "dur": 633, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484326302, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484326642, "dur": 432, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484327079, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484327241, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484327327, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484327657, "dur": 1209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484328912, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484330454, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484330890, "dur": 896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484331786, "dur": 320, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484332111, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484332987, "dur": 422, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484333416, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484333759, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484333884, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484334347, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484334530, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484334848, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484335039, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484335396, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484335687, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484336271, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484336434, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484336839, "dur": 504, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484337354, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484338468, "dur": 1502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484340164, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484340651, "dur": 784, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484341459, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484342141, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484342228, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484342869, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748526484343228, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484343437, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484343521, "dur": 555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484344078, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484344196, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1748526484344475, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484344646, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_18DD15F206BB0EF4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484344739, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484344872, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484345157, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484345315, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484345408, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484345567, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484345720, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484345791, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484346251, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484346366, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484346509, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484346618, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484346842, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484346954, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484347467, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484347778, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484347899, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484348116, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484348528, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484348646, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484349234, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484349331, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484349621, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484351133, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484351534, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnity.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484351640, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484351800, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484352199, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484352339, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484352514, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484352771, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484353070, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484353176, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484353271, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484353341, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484354083, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484354454, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484354583, "dur": 1214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484355803, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484356134, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484356268, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748526484356391, "dur": 567, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484356978, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484357195, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484357399, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484357495, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484357856, "dur": 2251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484360107, "dur": 2946, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484366384, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748526484363054, "dur": 4278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484367333, "dur": 3628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484370962, "dur": 3937, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748526484374900, "dur": 681, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.AspNetCore.Server.Kestrel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748526484370962, "dur": 9786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484381440, "dur": 561, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Editor/SettingsProvider/ProjectSettings/ScriptReferenceResolverSettings.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748526484380749, "dur": 2831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484383580, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484385079, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484385615, "dur": 1181, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/2D/ShapeEditor/Selection/ISelectable.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748526484385615, "dur": 2351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484387966, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484389772, "dur": 717, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/TabbedView/TabbedView.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748526484389313, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484391271, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484392050, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484393228, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484394050, "dur": 1256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484395306, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484396517, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484398008, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484399883, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484401265, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484402090, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484402927, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484403720, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484404647, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484405978, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484406783, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484407559, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484408797, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484410581, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484411394, "dur": 1068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484412463, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484412675, "dur": 1939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484414614, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484414778, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484414873, "dur": 1969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484416886, "dur": 9637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484426523, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484427162, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_EA61D4CF7C90DCE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484427324, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484427521, "dur": 1771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484429325, "dur": 1379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484430705, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484430782, "dur": 3216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484434011, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484434106, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484436778, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484436860, "dur": 4167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484441028, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484441181, "dur": 5222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484446403, "dur": 1448, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484447889, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_A1CE29DB57A110D1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484448057, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484448199, "dur": 4542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484452742, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484453065, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484453213, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484453374, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484453479, "dur": 3395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484456874, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484456941, "dur": 2849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484459791, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484460116, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484460185, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484461187, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484462408, "dur": 143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484462811, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484463004, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484463131, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484463668, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484464520, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748526484464770, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484464829, "dur": 604, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484465433, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484465545, "dur": 1234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484466780, "dur": 1435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484468216, "dur": 96720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484564939, "dur": 4287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484569269, "dur": 5235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484574506, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484574662, "dur": 2954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484577616, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484577694, "dur": 4271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484581965, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484582070, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484584074, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484584141, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484587794, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748526484587958, "dur": 5751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484593724, "dur": 5570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748526484599315, "dur": 645555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484263380, "dur": 61639, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484325020, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484325672, "dur": 630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484326302, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484326355, "dur": 755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484327110, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484327229, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484327350, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484327511, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484328960, "dur": 1328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484330312, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484330942, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484331249, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484331425, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484332430, "dur": 573, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484333015, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484333552, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484333786, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484334112, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484334375, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484334591, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484334872, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484335174, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484335436, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484335802, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484336366, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484336540, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484336858, "dur": 526, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484337385, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484337461, "dur": 1665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484339127, "dur": 855, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484340132, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484340618, "dur": 865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484341491, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484342360, "dur": 1256, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1748526484343617, "dur": 3213, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1748526484346830, "dur": 1430, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1748526484341864, "dur": 6397, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484348261, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484348486, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484348616, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484348734, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484348811, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484349333, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484349623, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484350023, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484350140, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484350433, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484350612, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484350840, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484351223, "dur": 428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484351652, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484351855, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484351988, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484352321, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484352447, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484352660, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484353028, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484353130, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484353242, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484353362, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484354034, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484354435, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484354535, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748526484355755, "dur": 362, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484356120, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484356258, "dur": 642, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484356902, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484357112, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484357308, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484357427, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484357587, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484357725, "dur": 2358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484360084, "dur": 2775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484366145, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.IO.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748526484362859, "dur": 4260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484367120, "dur": 3587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484370889, "dur": 1123, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748526484372013, "dur": 3371, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.VisualBasic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748526484370708, "dur": 9888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484381410, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.burst@7a907cf5a459/Unity.Burst.CodeGen/FunctionPointerInvokeTransform.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748526484380597, "dur": 2310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484382907, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484384936, "dur": 617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484385553, "dur": 1263, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/AssemblyInfo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748526484385553, "dur": 2408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484387961, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484389304, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484390175, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484391178, "dur": 834, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484392012, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484393167, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484393966, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484395253, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484396408, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484397897, "dur": 1741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484399639, "dur": 1555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484401195, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484402006, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484402878, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484403665, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484404604, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484405926, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484406741, "dur": 750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484407492, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484408715, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484410475, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484411292, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484412355, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484412988, "dur": 3279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484416360, "dur": 1263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484417675, "dur": 14444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484432120, "dur": 879, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484433009, "dur": 3374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484436424, "dur": 1707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484438131, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484438205, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484438808, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484439009, "dur": 7013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484446023, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484446188, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484446851, "dur": 15906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484462757, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484463046, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484463156, "dur": 2435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484465592, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484465747, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484465829, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484466416, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484466726, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484466838, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484467195, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484467512, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748526484467619, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484467841, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484467959, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484468234, "dur": 98127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484566365, "dur": 3208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484569575, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484569667, "dur": 4653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484574345, "dur": 3760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484578106, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484578165, "dur": 3341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484581507, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484581574, "dur": 5766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484587395, "dur": 4908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484592304, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484592389, "dur": 5149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748526484597539, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484597804, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484597937, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748526484598003, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484598156, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484598295, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484598460, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484598526, "dur": 773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748526484599299, "dur": 645593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484263381, "dur": 61761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484325143, "dur": 910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484326105, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484326309, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484326608, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484327108, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484327222, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484327362, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484327596, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484327785, "dur": 1594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484329380, "dur": 1388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484330773, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484331112, "dur": 845, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484331962, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484332456, "dur": 631, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484333103, "dur": 375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484333479, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484333649, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484333881, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484334283, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484334552, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484334832, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484335150, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484335294, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484335867, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484336364, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484336695, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484336854, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484337503, "dur": 1191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484338722, "dur": 1218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748526484339942, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484340045, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484340567, "dur": 868, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484341447, "dur": 664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484342112, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484342203, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484342515, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484342587, "dur": 504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484343194, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484343796, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484343953, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748526484344170, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484344253, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484344377, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484344557, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748526484344907, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484345186, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484345321, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484345463, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484345705, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484345778, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484346028, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748526484346102, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484346476, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484346559, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484346682, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484346995, "dur": 744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484347740, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484347846, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484348064, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484348165, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484348241, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484348522, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484348621, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484348803, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484348907, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484349279, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748526484349531, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484349698, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484350107, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484350399, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484350545, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484350750, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484351158, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484351609, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484351768, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484352250, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484352391, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484352566, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484352831, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484353103, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484353184, "dur": 788, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484354030, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484354286, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484354439, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484354573, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484355794, "dur": 330, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484356127, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484356263, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484356907, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484357117, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484357344, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748526484357490, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484357787, "dur": 2343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484360130, "dur": 2843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484366240, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger/System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484362974, "dur": 4323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484367298, "dur": 3608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484370907, "dur": 2764, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484373672, "dur": 1719, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484375392, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484370907, "dur": 9805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484380712, "dur": 2503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484383216, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484383284, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484383525, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484385106, "dur": 1651, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Overrides/ChannelMixerEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748526484385106, "dur": 2843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484387949, "dur": 1306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484389256, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484390158, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484391152, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484391988, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484393153, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484393968, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484395272, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484396454, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484397951, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484399792, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484401212, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484402011, "dur": 860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484402871, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484403630, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484404560, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484405860, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484406689, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484407419, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484408656, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484410436, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484411231, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484412309, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484413068, "dur": 32239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484445308, "dur": 467, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484445868, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484446345, "dur": 5954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484452300, "dur": 869, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484453187, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484453347, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484453764, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484455852, "dur": 867, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484456731, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484456834, "dur": 4423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484461258, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484461463, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_E30EE1A78470D274.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484461620, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484462443, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484462537, "dur": 1034, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484463573, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484463910, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484464033, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484464689, "dur": 482, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484465219, "dur": 1524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484466744, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484466941, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484467570, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484468158, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748526484468250, "dur": 96609, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484564864, "dur": 4771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484569637, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484570206, "dur": 4555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484574762, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484574835, "dur": 2725, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484577561, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484577620, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484579575, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484579636, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484582460, "dur": 2407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484584880, "dur": 5871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484590751, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484590823, "dur": 3948, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484594771, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484595388, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484595552, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484595851, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484596234, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484597476, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484597626, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484597750, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484597915, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598049, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598155, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598233, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598410, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598480, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484598821, "dur": 364480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484963500, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526484963303, "dur": 1562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526484965410, "dur": 230, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526485169983, "dur": 457, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526484965920, "dur": 204527, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748526485174389, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526485174381, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526485174468, "dur": 52, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748526485174524, "dur": 693, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1748526485175218, "dur": 69616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484263385, "dur": 61682, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484325068, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484326116, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484326320, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484326939, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484327176, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484327285, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484327448, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484327598, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484327814, "dur": 2626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484330441, "dur": 418, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484330864, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484331781, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484332045, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484332966, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484333266, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484333712, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484333814, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484334298, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484334460, "dur": 379, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484334839, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484334968, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484335311, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484335657, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484336186, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484336423, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484336826, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484337189, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484338326, "dur": 1629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484339996, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484340434, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484340840, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484341849, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484342180, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484342288, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484342549, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748526484342741, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748526484343231, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484343411, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484343479, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748526484343865, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484344182, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484344259, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748526484344639, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484344719, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484344847, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484345008, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484345321, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484345469, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484345709, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484345781, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484346061, "dur": 398, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484346462, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484346604, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484346908, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484346971, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484347084, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484347617, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484347806, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484347916, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748526484348016, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484348145, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484348232, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484348371, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484348794, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484348918, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748526484349257, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484349399, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484349534, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484349687, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484350106, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484350379, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484350585, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484350770, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484351203, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484351827, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484352309, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484352429, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484352634, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484352952, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484353116, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484353208, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484353298, "dur": 532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484353835, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484353961, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484354041, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484354446, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484355339, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484356070, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484356239, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484356638, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748526484356771, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484357080, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484357285, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484357461, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484357735, "dur": 2654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484360390, "dur": 3372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484366505, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Security.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748526484363763, "dur": 4436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484371082, "dur": 4041, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748526484375123, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner/System.AppContext.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748526484368200, "dur": 8193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484376393, "dur": 5011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484381405, "dur": 2677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484384082, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484385237, "dur": 1598, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.render-pipelines.universal@0b68ece7aa54/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Skin.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748526484385237, "dur": 2738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484387976, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484389441, "dur": 1043, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.shadergraph@afc42d8f5d23/Editor/Drawing/Inspector/PropertyDrawers/SubGraphOutputNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748526484389441, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484391528, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484392343, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484393493, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484394516, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484395755, "dur": 1284, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484397039, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484398390, "dur": 1981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484400371, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484401519, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484402357, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484403213, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484404006, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484405539, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Connections/IUnitConnectionDebugData.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748526484405048, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484406683, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484407413, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484408605, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484410415, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484411210, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484412263, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484412708, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484412765, "dur": 3561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484416327, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484416876, "dur": 595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484417507, "dur": 8695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484426202, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484426731, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484426883, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484427004, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_F0EDAB69F288A2F3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484427152, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484427364, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_70C63ADD63245F54.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484427494, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484427646, "dur": 4067, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484431714, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484431780, "dur": 4204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484435985, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484436149, "dur": 3409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484439559, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484439740, "dur": 6747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484446487, "dur": 776, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484447267, "dur": 1661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484448928, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484449118, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484449494, "dur": 7588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484457082, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484457210, "dur": 977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484458217, "dur": 5775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484463994, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484464437, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484464549, "dur": 2118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484466668, "dur": 754, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484467434, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484467595, "dur": 448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484468044, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484468148, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748526484468218, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484468433, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526484468580, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484469528, "dur": 492916, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484963286, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526484964591, "dur": 154758, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748526485120073, "dur": 1285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748526485122097, "dur": 403, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526485241027, "dur": 294, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748526485122698, "dur": 118632, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748526485244188, "dur": 594, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1748526485247954, "dur": 695, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 3915, "ts": 1748526485269253, "dur": 2584, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 3915, "ts": 1748526485271873, "dur": 2273, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 3915, "ts": 1748526485264026, "dur": 10882, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}