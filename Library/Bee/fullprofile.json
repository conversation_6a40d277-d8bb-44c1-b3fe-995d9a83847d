{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 27554, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 27554, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 27554, "tid": 4321, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 27554, "tid": 4321, "ts": 1748527153370177, "dur": 672, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153375661, "dur": 755, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 27554, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 27554, "tid": 1, "ts": 1748527150846009, "dur": 7068, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748527150853080, "dur": 55811, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 27554, "tid": 1, "ts": 1748527150908899, "dur": 42834, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153376420, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 27554, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150844235, "dur": 9712, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150853949, "dur": 2508209, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150854716, "dur": 2885, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150857605, "dur": 976, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150858583, "dur": 13413, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872000, "dur": 368, "ph": "X", "name": "ProcessMessages 2654", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872370, "dur": 54, "ph": "X", "name": "ReadAsync 2654", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872426, "dur": 3, "ph": "X", "name": "ProcessMessages 8130", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872433, "dur": 39, "ph": "X", "name": "ReadAsync 8130", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872479, "dur": 1, "ph": "X", "name": "ProcessMessages 1366", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872481, "dur": 37, "ph": "X", "name": "ReadAsync 1366", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872524, "dur": 1, "ph": "X", "name": "ProcessMessages 1416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872525, "dur": 57, "ph": "X", "name": "ReadAsync 1416", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872584, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872586, "dur": 27, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872615, "dur": 1, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872616, "dur": 45, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872663, "dur": 1, "ph": "X", "name": "ProcessMessages 1446", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872664, "dur": 19, "ph": "X", "name": "ReadAsync 1446", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872694, "dur": 46, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872741, "dur": 1, "ph": "X", "name": "ProcessMessages 1754", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872742, "dur": 42, "ph": "X", "name": "ReadAsync 1754", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872788, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872789, "dur": 22, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872832, "dur": 31, "ph": "X", "name": "ReadAsync 754", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872865, "dur": 1, "ph": "X", "name": "ProcessMessages 1381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872867, "dur": 27, "ph": "X", "name": "ReadAsync 1381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872907, "dur": 30, "ph": "X", "name": "ReadAsync 1163", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872940, "dur": 49, "ph": "X", "name": "ReadAsync 1179", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150872995, "dur": 38, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873034, "dur": 1, "ph": "X", "name": "ProcessMessages 1879", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873036, "dur": 50, "ph": "X", "name": "ReadAsync 1879", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873088, "dur": 11, "ph": "X", "name": "ProcessMessages 1684", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873100, "dur": 27, "ph": "X", "name": "ReadAsync 1684", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873130, "dur": 31, "ph": "X", "name": "ReadAsync 1226", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873168, "dur": 47, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873216, "dur": 1, "ph": "X", "name": "ProcessMessages 1546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873218, "dur": 33, "ph": "X", "name": "ReadAsync 1546", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873252, "dur": 1, "ph": "X", "name": "ProcessMessages 1426", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873254, "dur": 30, "ph": "X", "name": "ReadAsync 1426", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873291, "dur": 112, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873407, "dur": 2, "ph": "X", "name": "ProcessMessages 4297", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873410, "dur": 18, "ph": "X", "name": "ReadAsync 4297", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873434, "dur": 248, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873707, "dur": 131, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150873868, "dur": 370, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874241, "dur": 23, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874266, "dur": 17, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874285, "dur": 22, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874310, "dur": 56, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874386, "dur": 43, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874430, "dur": 1, "ph": "X", "name": "ProcessMessages 1201", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874432, "dur": 39, "ph": "X", "name": "ReadAsync 1201", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874472, "dur": 1, "ph": "X", "name": "ProcessMessages 1114", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874474, "dur": 17, "ph": "X", "name": "ReadAsync 1114", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874495, "dur": 222, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874720, "dur": 24, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874747, "dur": 25, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874773, "dur": 21, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874795, "dur": 28, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874850, "dur": 141, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150874992, "dur": 48, "ph": "X", "name": "ProcessMessages 1191", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875041, "dur": 18, "ph": "X", "name": "ReadAsync 1191", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875061, "dur": 1, "ph": "X", "name": "ProcessMessages 1302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875063, "dur": 49, "ph": "X", "name": "ReadAsync 1302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875113, "dur": 1, "ph": "X", "name": "ProcessMessages 1173", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875115, "dur": 22, "ph": "X", "name": "ReadAsync 1173", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875218, "dur": 27, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875249, "dur": 1, "ph": "X", "name": "ProcessMessages 2649", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875252, "dur": 96, "ph": "X", "name": "ReadAsync 2649", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875350, "dur": 204, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875555, "dur": 1, "ph": "X", "name": "ProcessMessages 1573", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875557, "dur": 59, "ph": "X", "name": "ReadAsync 1573", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875617, "dur": 1, "ph": "X", "name": "ProcessMessages 1958", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875619, "dur": 32, "ph": "X", "name": "ReadAsync 1958", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875653, "dur": 1, "ph": "X", "name": "ProcessMessages 1564", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875654, "dur": 40, "ph": "X", "name": "ReadAsync 1564", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875697, "dur": 39, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875737, "dur": 1, "ph": "X", "name": "ProcessMessages 1212", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875739, "dur": 35, "ph": "X", "name": "ReadAsync 1212", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875778, "dur": 60, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875841, "dur": 6, "ph": "X", "name": "ProcessMessages 1840", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875849, "dur": 22, "ph": "X", "name": "ReadAsync 1840", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875897, "dur": 54, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875952, "dur": 1, "ph": "X", "name": "ProcessMessages 1351", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875953, "dur": 18, "ph": "X", "name": "ReadAsync 1351", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875972, "dur": 1, "ph": "X", "name": "ProcessMessages 962", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150875974, "dur": 28, "ph": "X", "name": "ReadAsync 962", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876005, "dur": 33, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876041, "dur": 38, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876080, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876081, "dur": 49, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876131, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876133, "dur": 102, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876236, "dur": 1, "ph": "X", "name": "ProcessMessages 2240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876238, "dur": 246, "ph": "X", "name": "ReadAsync 2240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876487, "dur": 26, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876515, "dur": 26, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876589, "dur": 22, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876612, "dur": 1, "ph": "X", "name": "ProcessMessages 1842", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876618, "dur": 25, "ph": "X", "name": "ReadAsync 1842", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876647, "dur": 18, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876674, "dur": 53, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876736, "dur": 21, "ph": "X", "name": "ReadAsync 1021", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876758, "dur": 1, "ph": "X", "name": "ProcessMessages 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876760, "dur": 35, "ph": "X", "name": "ReadAsync 1204", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876797, "dur": 43, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876842, "dur": 23, "ph": "X", "name": "ReadAsync 1174", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876867, "dur": 31, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876900, "dur": 36, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150876964, "dur": 33, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877002, "dur": 1, "ph": "X", "name": "ProcessMessages 1799", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877004, "dur": 75, "ph": "X", "name": "ReadAsync 1799", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877080, "dur": 1, "ph": "X", "name": "ProcessMessages 2003", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877082, "dur": 43, "ph": "X", "name": "ReadAsync 2003", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877125, "dur": 1, "ph": "X", "name": "ProcessMessages 1719", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877127, "dur": 52, "ph": "X", "name": "ReadAsync 1719", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877193, "dur": 1, "ph": "X", "name": "ProcessMessages 1437", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877194, "dur": 17, "ph": "X", "name": "ReadAsync 1437", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877214, "dur": 22, "ph": "X", "name": "ReadAsync 1001", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877243, "dur": 41, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877285, "dur": 1, "ph": "X", "name": "ProcessMessages 1362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877287, "dur": 33, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877320, "dur": 1, "ph": "X", "name": "ProcessMessages 1052", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877322, "dur": 31, "ph": "X", "name": "ReadAsync 1052", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877355, "dur": 40, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877396, "dur": 1, "ph": "X", "name": "ProcessMessages 1480", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877398, "dur": 49, "ph": "X", "name": "ReadAsync 1480", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877451, "dur": 39, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877496, "dur": 105, "ph": "X", "name": "ReadAsync 1148", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877602, "dur": 1, "ph": "X", "name": "ProcessMessages 2266", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877604, "dur": 38, "ph": "X", "name": "ReadAsync 2266", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877649, "dur": 20, "ph": "X", "name": "ReadAsync 1233", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877672, "dur": 43, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877717, "dur": 56, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877775, "dur": 42, "ph": "X", "name": "ReadAsync 1391", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877821, "dur": 68, "ph": "X", "name": "ReadAsync 1331", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877890, "dur": 26, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877972, "dur": 1, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877974, "dur": 24, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150877999, "dur": 1, "ph": "X", "name": "ProcessMessages 1924", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878001, "dur": 19, "ph": "X", "name": "ReadAsync 1924", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878069, "dur": 34, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878105, "dur": 1, "ph": "X", "name": "ProcessMessages 2184", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878106, "dur": 31, "ph": "X", "name": "ReadAsync 2184", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878140, "dur": 93, "ph": "X", "name": "ReadAsync 1149", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878236, "dur": 26, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878268, "dur": 1, "ph": "X", "name": "ProcessMessages 2173", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878270, "dur": 25, "ph": "X", "name": "ReadAsync 2173", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878298, "dur": 136, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878439, "dur": 1, "ph": "X", "name": "ProcessMessages 3040", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878441, "dur": 33, "ph": "X", "name": "ReadAsync 3040", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878495, "dur": 1, "ph": "X", "name": "ProcessMessages 927", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878524, "dur": 46, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878573, "dur": 11, "ph": "X", "name": "ProcessMessages 2168", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878585, "dur": 30, "ph": "X", "name": "ReadAsync 2168", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878651, "dur": 1, "ph": "X", "name": "ProcessMessages 1250", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878653, "dur": 168, "ph": "X", "name": "ReadAsync 1250", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878825, "dur": 1, "ph": "X", "name": "ProcessMessages 1801", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878827, "dur": 48, "ph": "X", "name": "ReadAsync 1801", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878911, "dur": 21, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878935, "dur": 2, "ph": "X", "name": "ProcessMessages 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878941, "dur": 56, "ph": "X", "name": "ReadAsync 4780", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150878998, "dur": 1, "ph": "X", "name": "ProcessMessages 2238", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879000, "dur": 50, "ph": "X", "name": "ReadAsync 2238", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879051, "dur": 1, "ph": "X", "name": "ProcessMessages 1667", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879052, "dur": 42, "ph": "X", "name": "ReadAsync 1667", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879096, "dur": 1, "ph": "X", "name": "ProcessMessages 1186", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879097, "dur": 36, "ph": "X", "name": "ReadAsync 1186", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879135, "dur": 1, "ph": "X", "name": "ProcessMessages 1256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879136, "dur": 26, "ph": "X", "name": "ReadAsync 1256", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879165, "dur": 336, "ph": "X", "name": "ReadAsync 893", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879506, "dur": 1, "ph": "X", "name": "ProcessMessages 2058", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879508, "dur": 63, "ph": "X", "name": "ReadAsync 2058", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879573, "dur": 3, "ph": "X", "name": "ProcessMessages 6622", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879576, "dur": 134, "ph": "X", "name": "ReadAsync 6622", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879712, "dur": 114, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879833, "dur": 25, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879860, "dur": 25, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150879888, "dur": 149, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880040, "dur": 41, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880083, "dur": 51, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880136, "dur": 1, "ph": "X", "name": "ProcessMessages 1933", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880138, "dur": 17, "ph": "X", "name": "ReadAsync 1933", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880173, "dur": 29, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880204, "dur": 41, "ph": "X", "name": "ReadAsync 1121", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880246, "dur": 1, "ph": "X", "name": "ProcessMessages 1178", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880248, "dur": 27, "ph": "X", "name": "ReadAsync 1178", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880278, "dur": 469, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150880749, "dur": 261, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150881013, "dur": 155, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150881169, "dur": 407, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150881582, "dur": 552, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150882136, "dur": 145, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150882282, "dur": 19, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150882302, "dur": 512, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150882819, "dur": 148, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150882969, "dur": 452, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150883423, "dur": 47, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150883474, "dur": 476, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150883953, "dur": 158, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150884119, "dur": 19, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150884150, "dur": 633, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150884785, "dur": 546, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150885347, "dur": 136, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150885486, "dur": 11, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150885498, "dur": 407, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150885935, "dur": 128, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150886067, "dur": 1409, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150887477, "dur": 1, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150887479, "dur": 224, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150887706, "dur": 1096, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150888809, "dur": 2, "ph": "X", "name": "ProcessMessages 3187", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150888811, "dur": 106, "ph": "X", "name": "ReadAsync 3187", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150888936, "dur": 154, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150889091, "dur": 20, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150889112, "dur": 224, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150889340, "dur": 181, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150889542, "dur": 631, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890175, "dur": 8, "ph": "X", "name": "ProcessMessages 3736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890184, "dur": 249, "ph": "X", "name": "ReadAsync 3736", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890444, "dur": 58, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890506, "dur": 265, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890782, "dur": 93, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890879, "dur": 16, "ph": "X", "name": "ReadAsync 1257", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890897, "dur": 26, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890925, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150890927, "dur": 211, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891152, "dur": 57, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891216, "dur": 200, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891418, "dur": 1, "ph": "X", "name": "ProcessMessages 2700", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891420, "dur": 143, "ph": "X", "name": "ReadAsync 2700", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891565, "dur": 23, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891591, "dur": 201, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891794, "dur": 5, "ph": "X", "name": "ProcessMessages 837", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150891800, "dur": 676, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150892556, "dur": 2, "ph": "X", "name": "ProcessMessages 3522", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150892559, "dur": 104, "ph": "X", "name": "ReadAsync 3522", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150892665, "dur": 555, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150893225, "dur": 453, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150893683, "dur": 17, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150893703, "dur": 18, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150893723, "dur": 643, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150894370, "dur": 435, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150894817, "dur": 152, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150894973, "dur": 524, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150895545, "dur": 8, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150895554, "dur": 89, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150895646, "dur": 519, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150896177, "dur": 153, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150896332, "dur": 422, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150896757, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150896759, "dur": 29, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150896791, "dur": 293, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150897088, "dur": 397, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150897488, "dur": 603, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150898249, "dur": 2585, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150900837, "dur": 220, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901059, "dur": 151, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901213, "dur": 17, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901232, "dur": 227, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901462, "dur": 210, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901675, "dur": 177, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901854, "dur": 54, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901910, "dur": 15, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150901928, "dur": 200, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902130, "dur": 35, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902166, "dur": 6, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902173, "dur": 80, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902255, "dur": 17, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902274, "dur": 188, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902465, "dur": 161, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902628, "dur": 47, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902682, "dur": 19, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902703, "dur": 29, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902734, "dur": 17, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902753, "dur": 22, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902777, "dur": 207, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150902987, "dur": 210, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903199, "dur": 37, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903239, "dur": 149, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903394, "dur": 15, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903411, "dur": 200, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903613, "dur": 25, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903641, "dur": 274, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903925, "dur": 1, "ph": "X", "name": "ProcessMessages 2352", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903927, "dur": 65, "ph": "X", "name": "ReadAsync 2352", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150903998, "dur": 22, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904022, "dur": 24, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904048, "dur": 27, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904077, "dur": 192, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904272, "dur": 95, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904369, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904393, "dur": 29, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904423, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904425, "dur": 26, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904453, "dur": 20, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904476, "dur": 197, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904676, "dur": 16, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904694, "dur": 17, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904713, "dur": 22, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904738, "dur": 29, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904769, "dur": 169, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150904940, "dur": 189, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905131, "dur": 14, "ph": "X", "name": "ReadAsync 974", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905147, "dur": 28, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905177, "dur": 181, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905360, "dur": 16, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905379, "dur": 41, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905424, "dur": 1, "ph": "X", "name": "ProcessMessages 963", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905426, "dur": 18, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905446, "dur": 189, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905638, "dur": 199, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150905838, "dur": 173, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906012, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906110, "dur": 68, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906179, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906185, "dur": 85, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906272, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906300, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906305, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906375, "dur": 63, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906440, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906497, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906516, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906559, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906562, "dur": 113, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906678, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906778, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906824, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906848, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150906922, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907003, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907078, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907170, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907202, "dur": 60, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907264, "dur": 68, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907335, "dur": 99, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907436, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907455, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907521, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907582, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907607, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907668, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907710, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907753, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907790, "dur": 120, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907912, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150907951, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908081, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908103, "dur": 75, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908180, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908200, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908249, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908334, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908387, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908430, "dur": 32, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908464, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908485, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908563, "dur": 120, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908718, "dur": 39, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908759, "dur": 183, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150908945, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909011, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909051, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909110, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909134, "dur": 158, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909294, "dur": 359, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909657, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909714, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150909871, "dur": 286, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910159, "dur": 53, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910214, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910239, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910284, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910369, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910403, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910502, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910564, "dur": 17, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910583, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910660, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910704, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910761, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910874, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910901, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910907, "dur": 83, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150910992, "dur": 80, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911075, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911103, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911125, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911153, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911178, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911180, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911241, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911267, "dur": 44, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911313, "dur": 191, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911511, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911516, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911591, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911593, "dur": 67, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911663, "dur": 152, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911816, "dur": 1, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911818, "dur": 24, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911844, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911919, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911924, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911958, "dur": 24, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150911984, "dur": 25, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912012, "dur": 44, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912058, "dur": 21, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912081, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912099, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912121, "dur": 21, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912144, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912354, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912356, "dur": 18, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912377, "dur": 24, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912403, "dur": 13, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912418, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912443, "dur": 28, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912473, "dur": 16, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912491, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912510, "dur": 173, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150912685, "dur": 8700, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150921390, "dur": 116, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150921508, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150921598, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150921677, "dur": 2002, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150923682, "dur": 4658, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150928368, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150928369, "dur": 382, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150928763, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150928843, "dur": 403, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150929315, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150929320, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150929348, "dur": 93, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150929443, "dur": 604, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150930093, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150930102, "dur": 142, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150930246, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150930306, "dur": 775, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931083, "dur": 298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931383, "dur": 103, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931488, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931628, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931720, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150931908, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150932012, "dur": 739, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150932753, "dur": 832, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150933587, "dur": 296, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150933885, "dur": 42, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150933929, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150934298, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150934388, "dur": 189, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150934579, "dur": 330, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150934922, "dur": 303, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150935229, "dur": 1252, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150936495, "dur": 95, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150936602, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150936768, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150936829, "dur": 662, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150937498, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150937618, "dur": 256, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150937883, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150937888, "dur": 12280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150950172, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150950175, "dur": 69, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150950246, "dur": 2, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150950248, "dur": 532, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150950782, "dur": 465, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150951248, "dur": 418, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150951672, "dur": 957, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150952632, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150952782, "dur": 627, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150953411, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150953569, "dur": 192, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150953763, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150953861, "dur": 368, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150954231, "dur": 810, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150955043, "dur": 1022, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150956068, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150956069, "dur": 358, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150956430, "dur": 337, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150956771, "dur": 424, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150957198, "dur": 19000, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150976228, "dur": 5, "ph": "X", "name": "ProcessMessages 788", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527150976236, "dur": 96519, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********072763, "dur": 4, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********072768, "dur": 52, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********072823, "dur": 60, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********072919, "dur": 32, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********072952, "dur": 78, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********073033, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********073062, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********073092, "dur": 1253, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********074348, "dur": 2499, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********076849, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********076851, "dur": 252, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********077107, "dur": 659, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********077773, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********077777, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********078065, "dur": 983, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********079052, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********079055, "dur": 75, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********079132, "dur": 111, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********079245, "dur": 977, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********080226, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********080495, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********080497, "dur": 301, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********080806, "dur": 702, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********081510, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********081735, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********081737, "dur": 616, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********082357, "dur": 514, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********082880, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********083100, "dur": 462, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********083565, "dur": 280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********083847, "dur": 6, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********083854, "dur": 325, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********084186, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********084190, "dur": 921, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********085114, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********085117, "dur": 524, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********085643, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********085668, "dur": 299, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********085970, "dur": 1438, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********087410, "dur": 255, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********087667, "dur": 182, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********087851, "dur": 356, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********088210, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********088356, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********088360, "dur": 1248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********089612, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********089615, "dur": 668, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********090285, "dur": 287, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********090574, "dur": 331, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********090908, "dur": 87, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********090997, "dur": 1233, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********092232, "dur": 638, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********092872, "dur": 91, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********092965, "dur": 710, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********093677, "dur": 752, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********094435, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********094439, "dur": 635, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********095077, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********095079, "dur": 278, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********095360, "dur": 618, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********095980, "dur": 369, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096351, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096405, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096454, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096477, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096557, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096581, "dur": 104, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096687, "dur": 45, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096734, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096756, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096787, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096839, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096863, "dur": 18, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096883, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096944, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096945, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096970, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********096998, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097061, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097117, "dur": 89, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097208, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097242, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097283, "dur": 70, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097360, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097385, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097449, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097475, "dur": 19, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097495, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097526, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097586, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097633, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097668, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097734, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097759, "dur": 24, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097784, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097828, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097860, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097906, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********097938, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098028, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098077, "dur": 115, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098204, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098248, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098335, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098378, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098441, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098502, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098554, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098582, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098630, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098663, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098705, "dur": 266, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********098973, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********099023, "dur": 446, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********099471, "dur": 62, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********099536, "dur": 147, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": **********099685, "dur": 1944457, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153044196, "dur": 287, "ph": "X", "name": "ProcessMessages 1400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153044489, "dur": 9023, "ph": "X", "name": "ReadAsync 1400", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153053524, "dur": 11, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153053537, "dur": 5727, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153059292, "dur": 4, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153059299, "dur": 193401, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153252708, "dur": 40, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153252750, "dur": 2652, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153255408, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153255411, "dur": 82618, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338035, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338038, "dur": 56, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338097, "dur": 81, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338181, "dur": 92, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338346, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153338353, "dur": 1184, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153339539, "dur": 28, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153339568, "dur": 2005, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153341577, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153341583, "dur": 603, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153342188, "dur": 17, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153342205, "dur": 12105, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354314, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354316, "dur": 77, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354396, "dur": 90, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354489, "dur": 98, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354590, "dur": 123, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354714, "dur": 23, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153354738, "dur": 2610, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153357359, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153357362, "dur": 581, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153357944, "dur": 23, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153357969, "dur": 211, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153358181, "dur": 305, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 27554, "tid": 12884901888, "ts": 1748527153358488, "dur": 3578, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153376435, "dur": 1506, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 27554, "tid": 8589934592, "ts": 1748527150841747, "dur": 110005, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748527150951754, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 27554, "tid": 8589934592, "ts": 1748527150951758, "dur": 1452, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153377944, "dur": 13, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 27554, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 27554, "tid": 4294967296, "ts": 1748527150757109, "dur": 2606428, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748527150763516, "dur": 74827, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748527153363641, "dur": 3691, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748527153366054, "dur": 49, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 27554, "tid": 4294967296, "ts": 1748527153367435, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153377959, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748527150851048, "dur": 2869, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527150853922, "dur": 18219, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527150872200, "dur": 78, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748527150872279, "dur": 99, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527150872826, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748527150872384, "dur": 33755, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527150906147, "dur": 2452247, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527153358527, "dur": 58, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527153358621, "dur": 1147, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748527150872336, "dur": 33818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150906157, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748527150906341, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150906687, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150906808, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150906964, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150907038, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150907108, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150907246, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150907324, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150907400, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150907500, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150907638, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150907727, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_2793B80490192449.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150907862, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150907933, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908015, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908098, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908184, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908281, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908373, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908441, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908534, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908598, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908675, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908762, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150908855, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150908934, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150909239, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150909315, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150909476, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150909537, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150910212, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150910367, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150910820, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150910956, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748527150911130, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150911187, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150911386, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150911482, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150912569, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748527150913153, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150913842, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150914473, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150915165, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150915816, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150916490, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150917190, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150917928, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150919065, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150919916, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150920605, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150921259, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150922004, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150922778, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150923423, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150924100, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150924792, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150925488, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150926171, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150926805, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150927466, "dur": 435, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150927901, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector2/Vector2Multiply.cs"}}, {"pid": 12345, "tid": 1, "ts": 1748527150927901, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150929077, "dur": 1242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150930349, "dur": 4329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150934679, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150934803, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150934873, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150935663, "dur": 5310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150940973, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150941459, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150941862, "dur": 1052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150942914, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150943346, "dur": 2189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150945536, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150945734, "dur": 1659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150947917, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150948405, "dur": 3607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150952013, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150952156, "dur": 1101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150953260, "dur": 1442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150954723, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150956681, "dur": 358, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150957090, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150957149, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150957691, "dur": 4401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150962093, "dur": 1909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150964018, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150964094, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748527150964385, "dur": 709, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150965118, "dur": 3693, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748527150968812, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748527150968911, "dur": 105859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********074775, "dur": 2976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********077752, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********077809, "dur": 2835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********080657, "dur": 2170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********082827, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********082887, "dur": 3169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********086100, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********088614, "dur": 2445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********091060, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********091130, "dur": 2985, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********094150, "dur": 2676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": **********097057, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********097272, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********097373, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********098139, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********098412, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********098603, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********098760, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********098919, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********099039, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********099239, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": **********100046, "dur": 2258368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150872337, "dur": 33823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150906162, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748527150906416, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150906567, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150906710, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150907140, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150907240, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150907347, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150907418, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150907583, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150907645, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150907821, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150907895, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908045, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908166, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150908273, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908375, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150908453, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908533, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150908592, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908655, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150908744, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150908847, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150908926, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748527150909157, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150909233, "dur": 431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150909688, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150912085, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150912262, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150912543, "dur": 9051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150921595, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150921904, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150921992, "dur": 2146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150924173, "dur": 3852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150928025, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150928206, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150928270, "dur": 2003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150930273, "dur": 279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150930594, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150930733, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150934176, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150934413, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150935278, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150935378, "dur": 2214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150937593, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150937932, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150938950, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150939973, "dur": 2517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150942490, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150942558, "dur": 1868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150944427, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150944626, "dur": 3129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150947756, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150947910, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_B243E984A9C458BA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150948019, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150949305, "dur": 4518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150953824, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150954023, "dur": 1045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150955069, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150955195, "dur": 1676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150956871, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150956944, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_B2C8007ACBA256CB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150957055, "dur": 1518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150958574, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150958658, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150959069, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150959519, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150960051, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150960617, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150961143, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150962129, "dur": 1891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150964021, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150964098, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150964344, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150964558, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150964761, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150964951, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748527150965272, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748527150965642, "dur": 3240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527150968882, "dur": 105816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********074699, "dur": 1857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********076557, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********077105, "dur": 3550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********080670, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********083404, "dur": 2717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********086171, "dur": 3707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********089930, "dur": 2777, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********092707, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********092762, "dur": 3481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********096244, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": **********096377, "dur": 3570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": **********099966, "dur": 2241880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748527153341847, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748527153341927, "dur": 685, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1748527153342613, "dur": 15799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150872344, "dur": 33834, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150906179, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150906591, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150906684, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150907042, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150907272, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150907396, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150907508, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150907634, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150907706, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150907860, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150907917, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908009, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908076, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908173, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908249, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908362, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908424, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908530, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908587, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908669, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908753, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150908841, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150908913, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748527150909153, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150909238, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748527150909395, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150909449, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748527150909684, "dur": 463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748527150910170, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748527150910828, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1748527150910938, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150911065, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150911139, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150911209, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150911267, "dur": 682, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748527150911954, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748527150912151, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748527150913139, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150913852, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150914481, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150915158, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150915807, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150916478, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150917173, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150917919, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150919060, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150919911, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150920602, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150921256, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150922018, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150922157, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150922828, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150923479, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150924162, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150924856, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150925539, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150926223, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150926935, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150927580, "dur": 872, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/Obsolete/IVariableUnit.cs"}}, {"pid": 12345, "tid": 3, "ts": 1748527150927580, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150929099, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150929768, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150931348, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150931559, "dur": 1616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150933176, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150933231, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150936859, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150937232, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150937318, "dur": 971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150938322, "dur": 868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150939209, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150939632, "dur": 975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150940643, "dur": 1831, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150942474, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150942539, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150942826, "dur": 1382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150944208, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150944282, "dur": 2578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150946861, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150947094, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150947983, "dur": 4878, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150952862, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150953035, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_A9B74A479D61B912.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150953122, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150954237, "dur": 1166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748527150955421, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748527150958184, "dur": 1348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150959582, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150960107, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150960684, "dur": 524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150961208, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150962132, "dur": 2822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150964954, "dur": 3930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748527150968884, "dur": 105955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********074840, "dur": 4605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********079475, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********081846, "dur": 2392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********084238, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********084375, "dur": 3444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********087867, "dur": 3613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********091481, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********091548, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********094047, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": **********094144, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********096676, "dur": 3496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": **********100188, "dur": 2258229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150872350, "dur": 33832, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150906184, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150906572, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150906815, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150906879, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150906933, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150907031, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150907128, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150907262, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150907352, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150907491, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150907617, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150907707, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150907821, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150907942, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908012, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908096, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908162, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908307, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908374, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908482, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908538, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908622, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908678, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908756, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150908827, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150908907, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150909066, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150909122, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150909198, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748527150909362, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150909505, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150911875, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150912351, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748527150912727, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748527150913146, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150913847, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150914477, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150915156, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150915802, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150916474, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150917166, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150917906, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150919037, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150919749, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150920579, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150921238, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150921980, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150922050, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150922795, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150923447, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150924125, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150924806, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150925505, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150926185, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150926825, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150927472, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150927905, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Scalar/ScalarNormalize.cs"}}, {"pid": 12345, "tid": 4, "ts": 1748527150927905, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150929047, "dur": 571, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150929619, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150929702, "dur": 2585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150932287, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150932410, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150932488, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150934365, "dur": 4779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150939145, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150939474, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150939858, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150942053, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150942220, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150942860, "dur": 1944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150944804, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150945035, "dur": 3651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150948687, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150948885, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150948953, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150949120, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150949332, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150949936, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150950510, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150951076, "dur": 855, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150951931, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150952848, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150954038, "dur": 1027, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150955095, "dur": 1441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150956537, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150956742, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150957100, "dur": 4976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150962076, "dur": 1976, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150964063, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150964142, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150964568, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150964911, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150965017, "dur": 574, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150965592, "dur": 2353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150967956, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748527150968039, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748527150968274, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150968503, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748527150968883, "dur": 105877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********074767, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********077412, "dur": 3428, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********080855, "dur": 3210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********084112, "dur": 2156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********086314, "dur": 1958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********088324, "dur": 3048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********091373, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********091433, "dur": 1941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********093374, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********093429, "dur": 1957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********095422, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": **********099475, "dur": 709, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": **********100191, "dur": 2258182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150872361, "dur": 33826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150906189, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150906695, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150906810, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150906863, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150907137, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150907244, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150907476, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150907597, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150907697, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150907803, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150907894, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150907967, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908069, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908175, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150908259, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908409, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908554, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908626, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150908712, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908858, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150908986, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150909081, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150909139, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150909285, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150909439, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150909512, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150909679, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150909739, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150910795, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150910878, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150910953, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911068, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911124, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911193, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911389, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911480, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150911548, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150912081, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1748527150912657, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150912771, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748527150913158, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150913832, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150914461, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150915146, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150915788, "dur": 574, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150916362, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150917034, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150917728, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150918903, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150919614, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150920424, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150921100, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150921768, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150922608, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150923246, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150923933, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150924609, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150925330, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150925990, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150926639, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150927286, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150927840, "dur": 580, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector4/Vector4PerSecond.cs"}}, {"pid": 12345, "tid": 5, "ts": 1748527150927840, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150928945, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150929775, "dur": 11820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150941595, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150941961, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150942069, "dur": 10674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150952744, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150953046, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_AA9CC9DBDD612E3E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150953097, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150953206, "dur": 1013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150954219, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150954291, "dur": 2354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150956645, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150957075, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150957134, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150957682, "dur": 4110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150961792, "dur": 3026, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150964829, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150965010, "dur": 1040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150966050, "dur": 1957, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150968021, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150968103, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150968545, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150968811, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748527150968888, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527150969113, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150969680, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153043663, "dur": 53, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527150971203, "dur": 2072571, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153052351, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1748527153047318, "dur": 5363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153053101, "dur": 239, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527153055016, "dur": 197985, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153253757, "dur": 1307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153255594, "dur": 205, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527153354634, "dur": 380, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748527153255943, "dur": 99078, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748527153357741, "dur": 608, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748527150872367, "dur": 33853, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150906227, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150906684, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150906760, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150906881, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150906953, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150907067, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150907230, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150907331, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150907459, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150907571, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150907665, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150907738, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150907879, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150907957, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908035, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150908118, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908221, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908389, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908546, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908624, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150908684, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908820, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150908895, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150908958, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150909057, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150909260, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150909332, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748527150909403, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748527150909658, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150910115, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748527150910785, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150911000, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150911077, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150911150, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150911205, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150911400, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150911464, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150911733, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150912034, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150912274, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748527150913071, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150913925, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150914550, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150915231, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150915882, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150916550, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150917251, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150917979, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150919116, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150919960, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150920655, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150921299, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150922057, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150922816, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150923467, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150924147, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150924829, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150925524, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150926207, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150926852, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150927589, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Ports/ControlInput.cs"}}, {"pid": 12345, "tid": 6, "ts": 1748527150927488, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150929016, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150929230, "dur": 2531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748527150931762, "dur": 323, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150932087, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Searcher.Editor.ref.dll_188082AF4C893B1A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150932159, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150932216, "dur": 1838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150934093, "dur": 2652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748527150936746, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150937065, "dur": 10649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748527150947715, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150948013, "dur": 920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150948955, "dur": 4798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748527150953753, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150954194, "dur": 448, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1748527150954643, "dur": 1891, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1748527150956534, "dur": 670, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1748527150953863, "dur": 3341, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150957204, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150957528, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150957857, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150958144, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150958487, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150958716, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150959145, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150959602, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150960142, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150960726, "dur": 518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150961244, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150962130, "dur": 2701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150964832, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748527150964913, "dur": 684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748527150965619, "dur": 3267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527150968886, "dur": 105966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********074853, "dur": 3627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********078505, "dur": 2598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********081104, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********081182, "dur": 2409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********083627, "dur": 1853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********085533, "dur": 3145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********088679, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********088740, "dur": 2105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********090875, "dur": 3291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********094196, "dur": 4778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": **********098975, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********099127, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********099191, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": **********099507, "dur": 1947821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527153053122, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748527153047332, "dur": 10109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748527153058859, "dur": 588, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527153338266, "dur": 504, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748527153060259, "dur": 278519, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748527153341829, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748527153341821, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748527153341919, "dur": 681, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1748527153342603, "dur": 15774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150872374, "dur": 33871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150906247, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150906582, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150906820, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150906873, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150906931, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150907052, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150907134, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150907218, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150907379, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150907477, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150907622, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150907693, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150907888, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908005, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150908082, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908190, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150908296, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908373, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150908478, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908623, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908713, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150908773, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150908837, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150908903, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150909050, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150909251, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150909316, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748527150909561, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748527150910109, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748527150910671, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150910770, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748527150910935, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150911103, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150911175, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150911252, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748527150911420, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150911503, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150911811, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748527150912337, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748527150912878, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748527150913119, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150913720, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150914360, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150915024, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150915677, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150916360, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150917047, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150917774, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150918919, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150919630, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150920441, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150921117, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150921780, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150922630, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150923259, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150923945, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150924631, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150925343, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150926007, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150926656, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150927294, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150927842, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Math/Vector3/Vector3Multiply.cs"}}, {"pid": 12345, "tid": 7, "ts": 1748527150927842, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150928905, "dur": 956, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150929861, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150929915, "dur": 4672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150934588, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150935079, "dur": 631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150935738, "dur": 5367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150941105, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150941429, "dur": 517, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150941965, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150942456, "dur": 13896, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150956352, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150956592, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150956799, "dur": 5398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150962223, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150962284, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150962540, "dur": 2409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150964950, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150965152, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748527150965448, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150965514, "dur": 3366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527150968882, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748527150968996, "dur": 105779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********074777, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********077091, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********077267, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********079622, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********079691, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********082450, "dur": 2785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********085273, "dur": 2809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********088083, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********088139, "dur": 2701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********090902, "dur": 3359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********094270, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": **********096816, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********096992, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********097385, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********097482, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********097714, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********098045, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********098333, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********098517, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********098665, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********099210, "dur": 752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": **********099974, "dur": 2257741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748527153357750, "dur": 607, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748527150872380, "dur": 33870, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150906250, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150906680, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150906802, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150906938, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150907046, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150907166, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150907252, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150907345, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150907412, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150907609, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150907666, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150907813, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150907891, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908002, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150908092, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908199, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908357, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150908420, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908577, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908632, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150908722, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150908811, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150908885, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150909048, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150909221, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150909502, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_C5A9EDBF39CB7C6A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150909584, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150909638, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150910175, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748527150910685, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150910819, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150910898, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911000, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911079, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911159, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911221, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911281, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150911339, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150911956, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150912088, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150912272, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150912766, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748527150913115, "dur": 571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150913687, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150914341, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150915015, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150915661, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150916335, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150917025, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150917720, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150918893, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150919589, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150920403, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150921088, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150921741, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150922588, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150923219, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150923908, "dur": 678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150924586, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150925303, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150925946, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150926613, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150927272, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150927824, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library/PackageCache/com.unity.visualscripting@1b53f46e931b/Runtime/VisualScripting.Flow/Framework/Variables/GetVariable.cs"}}, {"pid": 12345, "tid": 8, "ts": 1748527150927824, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150929147, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150929333, "dur": 2336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748527150931669, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150931913, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150931969, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_62FEAEE8B284189E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150932046, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150932108, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150934068, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748527150936746, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150937068, "dur": 1371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150938467, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150939144, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748527150940156, "dur": 1766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748527150941922, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150941992, "dur": 1588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748527150943580, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150943742, "dur": 4192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1748527150948010, "dur": 2871, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********073086, "dur": 496, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748527150951303, "dur": 122288, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": **********074694, "dur": 2388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********077083, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********077142, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********079492, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********079571, "dur": 2433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********082005, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********082069, "dur": 2267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********084336, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": **********084391, "dur": 3388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********087833, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********090632, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********093481, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********095826, "dur": 4191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": **********100036, "dur": 2258370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748527153361365, "dur": 894, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 27554, "tid": 4321, "ts": 1748527153379126, "dur": 3415, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 27554, "tid": 4321, "ts": 1748527153382574, "dur": 1972, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 27554, "tid": 4321, "ts": 1748527153372814, "dur": 13232, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}